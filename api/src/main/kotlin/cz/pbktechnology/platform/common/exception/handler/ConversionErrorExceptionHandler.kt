package cz.pbktechnology.platform.common.exception.handler

import com.fasterxml.jackson.databind.JsonMappingException
import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.ErrorResult
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import cz.pbktechnology.platform.common.util.ExceptionUtil.localizedMessageWithFallback
import io.micronaut.context.annotation.Replaces
import io.micronaut.core.convert.exceptions.ConversionErrorException
import io.micronaut.http.HttpRequest
import io.micronaut.http.annotation.Produces
import jakarta.inject.Singleton
import cz.pbktechnology.platform.common.exception.ConversionErrorException as PbkConversionErrorException

@Singleton
@Produces
@Replaces(bean = io.micronaut.http.server.exceptions.ConversionErrorHandler::class)
class ConversionErrorExceptionHandler(
    serverResponseMapperHolder: ServerResponseMapperHolder,
    correlationIdServerExtractor: CorrelationIdServerExtractor,
) : PbkContextAwareHandler<ConversionErrorException>(
        serverResponseMapperHolder = serverResponseMapperHolder,
        correlationIdServerExtractor = correlationIdServerExtractor,
    ) {
    override fun handleInContext(
        request: HttpRequest<*>,
        operation: String,
        exception: ConversionErrorException,
    ): PbkException {
        val pbkException =
            when (val cause = exception.conversionError.cause) {
                is JsonMappingException -> {
                    val validationError =
                        ErrorResult.ValidationError(
                            key =
                                cause.path
                                    .filterNot { reference -> reference.fieldName.isNullOrEmpty() }
                                    .joinToString(".") { reference -> reference.fieldName },
                            message = cause.originalMessage.orEmpty(),
                        )
                    PbkConversionErrorException(
                        listOf(validationError),
                        exception.localizedMessageWithFallback(),
                    )
                }

                else ->
                    PbkConversionErrorException(
                        errorMessage = exception.localizedMessageWithFallback(),
                    )
            }

        return pbkException
    }
}
