package cz.pbktechnology.platform.common.context.instrument

import io.opentelemetry.api.common.AttributesBuilder
import io.opentelemetry.instrumentation.api.instrumenter.AttributesExtractor

class MinioAttributesExtractor : AttributesExtractor<MinioRequest, Any> {
    override fun onStart(
        attributes: Attributes<PERSON>uilder,
        parentContext: io.opentelemetry.context.Context,
        request: MinioRequest,
    ) {
        attributes.put("bucket.name", request.bucketName)
        attributes.put("object.name", request.objectName)
        attributes.put("minio.operation", request.operationName)
    }

    override fun onEnd(
        attributes: AttributesBuilder,
        context: io.opentelemetry.context.Context,
        request: MinioRequest,
        response: Any?,
        error: Throwable?,
    ) {
        // No operation-specific handling
    }
}
