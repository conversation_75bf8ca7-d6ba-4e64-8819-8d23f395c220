package cz.pbktechnology.platform.common.exception.handler

import cz.pbktechnology.platform.common.context.Header
import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.helper.ErrorResult
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapper
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import cz.pbktechnology.platform.common.util.OptionalUtil.unwrap
import cz.pbktechnology.platform.common.util.extractOperationIdFromRequestContext
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.server.exceptions.ExceptionHandler
import org.slf4j.MDC

/**
 * This is for mapping of unexpected Exceptions to PbkException
 *
 */
abstract class ContextAwareHandler<T : Throwable>(
    private val serverResponseMapperHolder: ServerResponseMapperHolder,
    private val correlationIdServerExtractor: CorrelationIdServerExtractor,
) : ExceptionHandler<T, HttpResponse<Any>> {
    // TODO trace logging
    final override fun handle(
        request: HttpRequest<*>,
        exception: T,
    ): HttpResponse<Any> {
        try {
            val operation = extractOperationIdFromRequestContext()

            MDC.put(
                Header.X_CORRELATION_ID,
                correlationIdServerExtractor.extract()?.toString() ?: "?",
            )

            val handledException =
                handleInContext(
                    request = request,
                    operation = operation,
                    exception = exception,
                )

            val mappingResult =
                serverResponseMapperHolder[operation]
                    .logAndMapResponse(handledException, operation)

            return HttpResponse.status<Any>(mappingResult.response.status).body(
                setWasLoggedInErrorResult(mappingResult.response, mappingResult.errorResponsibility),
            )
        } finally {
            MDC.remove(Header.X_CORRELATION_ID)
        }
    }

    private fun setWasLoggedInErrorResult(
        response: HttpResponse<Any>,
        errorResponsibility: ServerResponseMapper.ErrorResponsibility,
    ): Any? =
        response.body.unwrap()?.let { responseBody ->
            if (responseBody is ErrorResult) {
                responseBody.copy(wasLogged = errorResponsibility.wasLoggedOut)
            } else {
                responseBody
            }
        }

    /**
     * Implementers should not log exceptions but do it in ServerResponseMapper implementation
     */
    abstract fun handleInContext(
        request: HttpRequest<*>,
        operation: String,
        exception: T,
    ): Throwable
}
