package cz.pbktechnology.platform.common.context.instrument

import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Prototype
import io.micronaut.context.annotation.Requires
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter
import io.opentelemetry.instrumentation.api.instrumenter.SpanKindExtractor
import jakarta.inject.Named

@Factory
class InstrumenterFactory {
    @Prototype
    @Requires(beans = [OpenTelemetry::class])
    @Named(CONSUMER_INSTRUMENTER)
    fun consumerInstrumenter(
        openTelemetry: OpenTelemetry,
        spanNameExtractor: MessagingSpanNameExtractor,
    ): Instrumenter<MessagingRequest, Any>? {
        val builder =
            Instrumenter.builder<MessagingRequest, Any>(
                openTelemetry,
                CONSUMER_INSTRUMENTER_SCOPE,
                spanNameExtractor,
            )

        builder.addAttributesExtractor(CorrelationIdExtractor())
        builder.addAttributesExtractor(ConsumerHeaderExtractor())

        return builder.buildInstrumenter(SpanKindExtractor.alwaysConsumer())
    }

    @Prototype
    @Requires(beans = [OpenTelemetry::class])
    @Named(JOB_INSTRUMENTER)
    fun jobInstrumenter(
        openTelemetry: OpenTelemetry,
        spanNameExtractor: JobSpanNameExtractor,
    ): Instrumenter<JobRequest, Any>? {
        val builder =
            Instrumenter.builder<JobRequest, Any>(
                openTelemetry,
                JOB_INSTRUMENTER_SCOPE,
                spanNameExtractor,
            )

        builder.addAttributesExtractor(CorrelationIdExtractor())

        return builder.buildInstrumenter(SpanKindExtractor.alwaysInternal())
    }

    @Prototype
    @Requires(beans = [OpenTelemetry::class])
    @Named(REDIS_INSTRUMENTER)
    fun redisInstrumenter(
        openTelemetry: OpenTelemetry,
        spanNameExtractor: RedisSpanNameExtractor,
    ): Instrumenter<RedisRequest, Any> =
        Instrumenter
            .builder<RedisRequest, Any>(
                openTelemetry,
                REDIS_INSTRUMENTER_SCOPE,
                spanNameExtractor,
            ).apply {
                addAttributesExtractor(CorrelationIdExtractor())
                addAttributesExtractor(RedisAttributesExtractor())
            }.buildInstrumenter(SpanKindExtractor.alwaysClient())

    @Prototype
    @Requires(beans = [OpenTelemetry::class])
    @Named(MINIO_INSTRUMENTER)
    fun minioInstrumenter(
        openTelemetry: OpenTelemetry,
        spanNameExtractor: MinioSpanNameExtractor,
    ): Instrumenter<MinioRequest, Any> =
        Instrumenter
            .builder<MinioRequest, Any>(
                openTelemetry,
                MINIO_INSTRUMENTER_SCOPE,
                spanNameExtractor,
            ).apply {
                addAttributesExtractor(CorrelationIdExtractor())
                addAttributesExtractor(MinioAttributesExtractor())
            }.buildInstrumenter(SpanKindExtractor.alwaysClient())

    companion object {
        const val CONSUMER_INSTRUMENTER_SCOPE = "cz.pbktechnology.platform.messaging.consumer"
        const val JOB_INSTRUMENTER_SCOPE = "cz.pbktechnology.platform.job"
        const val REDIS_INSTRUMENTER_SCOPE = "cz.pbktechnology.platform.redis"
        const val MINIO_INSTRUMENTER_SCOPE = "cz.pbktechnology.platform.minio"

        const val CONSUMER_INSTRUMENTER = "consumerInstrumenter"
        const val JOB_INSTRUMENTER = "jobInstrumenter"
        const val REDIS_INSTRUMENTER = "redisInstrumenter"
        const val MINIO_INSTRUMENTER = "minioInstrumenter"
    }
}
