package cz.pbktechnology.platform.common.exception.handler

import cz.pbktechnology.platform.common.client.ExternalNotFoundException
import cz.pbktechnology.platform.common.client.ExternalPbkNotFoundException
import cz.pbktechnology.platform.common.client.PbkClientException
import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Produces
import jakarta.inject.Singleton

/**
 * This handler is responsible for remapping 404 to 406
 * It is important to comply to PbkClientException contract (unwrap + rewrap)
 * so that rest of the pipeline works correctly
 */

@Singleton
@Produces
class NotFoundExceptionHandler(
    serverResponseMapperHolder: ServerResponseMapperHolder,
    correlationIdServerExtractor: CorrelationIdServerExtractor,
) : PbkContextAwareHandler<PbkClientException>(
        serverResponseMapperHolder = serverResponseMapperHolder,
        correlationIdServerExtractor = correlationIdServerExtractor,
    ) {
    override fun handleInContext(
        request: HttpRequest<*>,
        operation: String,
        exception: PbkClientException,
    ): PbkException {
        val cause = exception.cause
        when (cause) {
            is ExternalPbkNotFoundException -> {
                val externalError = cause.externalError

                val externalPbkNotFoundException =
                    ExternalPbkNotFoundException(
                        externalError = externalError.copy(httpStatus = HttpStatus.NOT_ACCEPTABLE),
                        cause = exception.cause,
                    )
                return PbkClientException(
                    errorMessage = exception.message ?: "MISSING MESSAGE",
                    errorSource = exception.errorSource,
                    cause = externalPbkNotFoundException,
                    httpStatus = HttpStatus.NOT_ACCEPTABLE,
                )
            }

            is ExternalNotFoundException -> {
                val externalNotFoundException =
                    ExternalNotFoundException(
                        cause = cause.cause,
                        reason = cause.reason,
                        resource = cause.resource,
                        httpStatus = HttpStatus.NOT_ACCEPTABLE,
                    )
                return PbkClientException(
                    errorMessage = exception.message ?: "MISSING MESSAGE",
                    errorSource = exception.errorSource,
                    cause = externalNotFoundException,
                    httpStatus = HttpStatus.NOT_ACCEPTABLE,
                )
            }

            else -> {
                return exception
            }
        }
    }
}
