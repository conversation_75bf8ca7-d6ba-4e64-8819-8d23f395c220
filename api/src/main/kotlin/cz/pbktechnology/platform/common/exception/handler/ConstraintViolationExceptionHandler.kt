package cz.pbktechnology.platform.common.exception.handler

import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.ErrorResult
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import cz.pbktechnology.platform.common.util.ExceptionUtil.localizedMessageWithFallback
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpRequest
import io.micronaut.http.annotation.Produces
import io.micronaut.http.server.exceptions.ExceptionHandler
import jakarta.inject.Singleton
import org.hibernate.validator.internal.engine.ConstraintViolationImpl
import javax.validation.ConstraintViolationException
import javax.validation.ElementKind
import cz.pbktechnology.platform.common.exception.ConstraintViolationException as PbkConstraintViolationException

@Produces
@Singleton
@Requires(classes = [ConstraintViolationException::class, ExceptionHandler::class])
@Replaces(bean = io.micronaut.validation.exceptions.ConstraintExceptionHandler::class)
class ConstraintViolationExceptionHandler(
    serverResponseMapperHolder: ServerResponseMapperHolder,
    correlationIdServerExtractor: CorrelationIdServerExtractor,
) : PbkContextAwareHandler<ConstraintViolationException>(
        serverResponseMapperHolder = serverResponseMapperHolder,
        correlationIdServerExtractor = correlationIdServerExtractor,
    ) {
    override fun handleInContext(
        request: HttpRequest<*>,
        operation: String,
        exception: ConstraintViolationException,
    ): PbkException {
        val validationErrors =
            exception.constraintViolations.map { constraintViolation ->
                ErrorResult.ValidationError(
                    key =
                        constraintViolation.propertyPath
                            .asSequence()
                            .filterNot { node -> node.kind == ElementKind.METHOD }
                            .filterNot { node -> node.kind == ElementKind.CONSTRUCTOR }
                            .filterNot { node -> node.kind == ElementKind.CROSS_PARAMETER }
                            .filterNot { node -> node.kind == ElementKind.PARAMETER }
                            .filterNot { node -> node.name.isNullOrEmpty() }
                            .joinToString(".") { node -> node.name },
                    message = constraintViolation.message,
                    metadata =
                        when (constraintViolation) {
                            is ConstraintViolationImpl ->
                                constraintViolation.getDynamicPayload(Map::class.java)

                            else -> null
                        },
                )
            }

        return PbkConstraintViolationException(
            validationErrors,
            exception.localizedMessageWithFallback(),
        )
    }
}
