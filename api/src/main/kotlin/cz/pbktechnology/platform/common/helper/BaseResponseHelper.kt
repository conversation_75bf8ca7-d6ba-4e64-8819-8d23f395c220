package cz.pbktechnology.platform.common.helper

import arrow.core.Either
import cz.pbktechnology.platform.common.context.ContextProvider
import cz.pbktechnology.platform.common.context.Header
import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.exception.InternalServerException
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.metrics.RequestsProcessingMetric
import cz.pbktechnology.platform.common.util.OptionalUtil.unwrap
import cz.pbktechnology.platform.common.util.extractRequestInfoFromRequestContext
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Value
import io.micronaut.http.HttpRequest
import io.micronaut.http.context.ServerRequestContext.currentRequest
import io.micronaut.http.server.netty.NettyHttpRequest
import jakarta.inject.Inject
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import mu.KotlinLogging

@Suppress("unused", "MemberVisibilityCanBePrivate")
abstract class BaseResponseHelper {
    @field:Property(
        name =
            "micronaut.application.name",
    ) // TODO services with more deployments per repo override
    // the service name in k8s. This is misleading information
    // in that case
    private lateinit var serviceName: String

    @Inject
    private lateinit var requestsProcessingMetric: RequestsProcessingMetric

    @Inject
    private lateinit var contextProvider: ContextProvider

    @Value("\${feature-toggles.api.abort-cancelled-requests:true}")
    private var isAbortCancelledRequestsEnabled: Boolean = true

    @Inject
    lateinit var correlationIdServerExtractor: CorrelationIdServerExtractor

    private val logger = KotlinLogging.logger {}

    open suspend fun <Response> perform(
        operation: String,
        action: suspend () -> Either<PbkException, Response>,
    ): Response =
        try {
            requestsProcessingMetric.incrementRequestsProcessingCount()

            storeLoggingContextToRequest(operation)
            contextProvider.withServerContext {
                coroutineScope {
                    async {
                        logger.info { extractRequestInfoFromRequestContext() }
                        abortWhenRequestCancelled() // TODO logging?
                        action.invoke().fold(
                            ifLeft = { pbkException -> throw pbkException },
                            ifRight = { data ->
                                logger.info { "${extractRequestInfoFromRequestContext()} success." }
                                data
                            },
                        )
                    }.await()
                }
            }
        } finally {
            requestsProcessingMetric.decrementRequestsProcessingCount()
        }

    /** call method without EITHER signature, just pull on thread and be happy:) */
    open suspend fun <Response> performDirect(
        operation: String,
        action: suspend () -> Response,
    ): Response =
        try {
            requestsProcessingMetric.incrementRequestsProcessingCount()

            storeLoggingContextToRequest(operation)
            contextProvider.withServerContext {
                coroutineScope {
                    async {
                        logger.info { extractRequestInfoFromRequestContext() }
                        abortWhenRequestCancelled()
                        action.invoke().also { logger.info { "${extractRequestInfoFromRequestContext()} success." } }
                    }.await()
                }
            }
        } finally {
            requestsProcessingMetric.decrementRequestsProcessingCount()
        }

    fun abortWhenRequestCancelled() {
        if (!(isAbortCancelledRequestsEnabled && currentRequest<Any>().isPresent)) return

        val nettyRequest =
            currentRequest<Any>().get() as? NettyHttpRequest
                ?: throw InternalServerException("Current request is not NettyHttpRequest")

        if (!nettyRequest.channelHandlerContext.channel().isActive) {
            throw InternalServerException("Request was cancelled")
        }
    }

    private fun constructRequestInformation(
        operation: String,
        request: HttpRequest<Any>,
    ) = "request: [@${request.method} ${request.uri}] operation: [$operation]"

    // since we exit withContext, we need to store logging in request and refresh in handlers
    private fun storeLoggingContextToRequest(operation: String) {
        currentRequest<Any>().unwrap()?.let { request ->
            val data =
                mutableMapOf(
                    Header.X_CORRELATION_ID to
                        correlationIdServerExtractor.extract(),
                    Header.OPERATION_REQUEST_ATTRIBUTE_KEY to operation,
                    Header.REQUEST_INFO_REQUEST_ATTRIBUTE_KEY to
                        constructRequestInformation(
                            operation = operation,
                            request = request,
                        ),
                )
            logger.trace { "Storing logging info to HttpRequest: [$data]" }

            request.attributes.apply {
                data.forEach {
                    put(it.key, it.value)
                }
            }
        } ?: logger.debug { "HttpRequest is not found. Contact platform team." }
    }
}
