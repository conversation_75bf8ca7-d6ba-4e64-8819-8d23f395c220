package cz.pbktechnology.platform.common.exception.handler

import com.fasterxml.jackson.core.JsonProcessingException
import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import cz.pbktechnology.platform.common.util.ExceptionUtil.localizedMessageWithFallback
import io.micronaut.context.annotation.Replaces
import io.micronaut.http.HttpRequest
import io.micronaut.http.annotation.Produces
import io.micronaut.http.server.exceptions.JsonExceptionHandler
import jakarta.inject.Singleton
import cz.pbktechnology.platform.common.exception.JsonProcessingException as PbkJsonProcessingException

@Singleton
@Produces
@Replaces(bean = JsonExceptionHandler::class)
class JsonProcessingExceptionHandler(
    serverResponseMapperHolder: ServerResponseMapperHolder,
    correlationIdServerExtractor: CorrelationIdServerExtractor,
) : PbkContextAwareHandler<JsonProcessingException>(
        serverResponseMapperHolder = serverResponseMapperHolder,
        correlationIdServerExtractor = correlationIdServerExtractor,
    ) {
    override fun handleInContext(
        request: HttpRequest<*>,
        operation: String,
        exception: JsonProcessingException,
    ) = PbkJsonProcessingException(exception.localizedMessageWithFallback())
}
