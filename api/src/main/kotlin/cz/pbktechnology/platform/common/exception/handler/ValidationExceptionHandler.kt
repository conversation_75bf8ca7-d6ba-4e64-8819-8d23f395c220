package cz.pbktechnology.platform.common.exception.handler

import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import cz.pbktechnology.platform.common.util.ExceptionUtil.localizedMessageWithFallback
import io.micronaut.http.HttpRequest
import io.micronaut.http.annotation.Produces
import jakarta.inject.Singleton
import javax.validation.ValidationException
import cz.pbktechnology.platform.common.exception.ValidationException as PbkValidationException

@Singleton
@Produces
class ValidationExceptionHandler(
    serverResponseMapperHolder: ServerResponseMapperHolder,
    correlationIdServerExtractor: CorrelationIdServerExtractor,
) : PbkContextAwareHandler<ValidationException>(
        serverResponseMapperHolder = serverResponseMapperHolder,
        correlationIdServerExtractor = correlationIdServerExtractor,
    ) {
    override fun handleInContext(
        request: HttpRequest<*>,
        operation: String,
        exception: ValidationException,
    ) = PbkValidationException(exception.localizedMessageWithFallback())
}
