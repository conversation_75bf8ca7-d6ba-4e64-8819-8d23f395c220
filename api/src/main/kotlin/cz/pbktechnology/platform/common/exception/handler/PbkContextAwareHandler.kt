package cz.pbktechnology.platform.common.exception.handler

import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import io.micronaut.http.HttpRequest

/**
 * For handling of exceptions to PbkExceptions
 * This is the extension point for Pbk
 */
abstract class PbkContextAwareHandler<T : Throwable>(
    serverResponseMapperHolder: ServerResponseMapperHolder,
    correlationIdServerExtractor: CorrelationIdServerExtractor,
) : ContextAwareHandler<T>(
        serverResponseMapperHolder = serverResponseMapperHolder,
        correlationIdServerExtractor = correlationIdServerExtractor,
    ) {
    abstract override fun handleInContext(
        request: HttpRequest<*>,
        operation: String,
        exception: T,
    ): PbkException
}
