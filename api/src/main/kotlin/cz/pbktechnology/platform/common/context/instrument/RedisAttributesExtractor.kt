package cz.pbktechnology.platform.common.context.instrument

import io.opentelemetry.api.common.AttributesBuilder
import io.opentelemetry.instrumentation.api.instrumenter.AttributesExtractor
import java.util.Optional

class RedisAttributesExtractor : AttributesExtractor<RedisRequest, Any> {
    override fun onStart(
        attributes: AttributesBuilder,
        parentContext: io.opentelemetry.context.Context,
        request: RedisRequest,
    ) {
        attributes.put("cache.name", request.cacheName)
        attributes.put("cache.key", request.cacheKey)
        attributes.put("cache.operation", request.operation.name)
    }

    override fun onEnd(
        attributes: AttributesBuilder,
        context: io.opentelemetry.context.Context,
        request: RedisRequest,
        response: Any?,
        error: Throwable?,
    ) {
        if (request.operation in listOf(RedisOperation.GET, RedisOperation.PUT_IF_ABSENT)) {
            val cacheHit =
                when (response) {
                    is Optional<*> -> response.isPresent
                    null -> false
                    else -> true
                }
            attributes.put("cache.hit", cacheHit)
        }
    }
}
