package cz.pbktechnology.platform.common.util

import cz.pbktechnology.platform.common.context.Header
import cz.pbktechnology.platform.common.util.OptionalUtil.unwrap
import io.micronaut.http.context.ServerRequestContext.currentRequest

fun extractRequestInfoFromRequestContext() =
    currentRequest<Any>()
        .unwrap()
        ?.attributes
        ?.get(
            Header.REQUEST_INFO_REQUEST_ATTRIBUTE_KEY,
            String::class.java,
        )?.unwrap()
        ?: extractOperationIdFromRequestContext() // backwards compatibility

fun extractOperationIdFromRequestContext() =
    (
        currentRequest<Any>()
            .unwrap()
            ?.attributes
            ?.get(
                Header.OPERATION_REQUEST_ATTRIBUTE_KEY,
                String::class.java,
            )?.unwrap()
            ?: "Missing ${Header.OPERATION_REQUEST_ATTRIBUTE_KEY} in currentRequest. Contact maintainers."
    )
