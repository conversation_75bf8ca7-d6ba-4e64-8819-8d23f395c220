package cz.pbktechnology.platform.common.exception.handler

import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import io.micronaut.http.HttpRequest
import io.micronaut.http.annotation.Error
import io.micronaut.http.annotation.Produces
import jakarta.inject.Singleton

@Singleton
@Produces
class GlobalExceptionHandler(
    serverResponseMapperHolder: ServerResponseMapperHolder,
    correlationIdServerExtractor: CorrelationIdServerExtractor,
) : ContextAwareHandler<Throwable>(
        serverResponseMapperHolder = serverResponseMapperHolder,
        correlationIdServerExtractor = correlationIdServerExtractor,
    ) {
    @Error(global = true)
    override fun handleInContext(
        request: HttpRequest<*>,
        operation: String,
        exception: Throwable,
    ): Throwable = exception
}
