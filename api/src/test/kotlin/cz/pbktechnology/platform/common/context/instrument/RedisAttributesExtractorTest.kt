package cz.pbktechnology.platform.common.context.instrument

import io.mockk.clearMocks
import io.mockk.mockk
import io.mockk.verify
import io.opentelemetry.api.common.AttributesBuilder
import io.opentelemetry.context.Context
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.Optional

class RedisAttributesExtractorTest {
    private val extractor = RedisAttributesExtractor()
    private val mockAttributesBuilder: AttributesBuilder = mockk(relaxed = true)
    private val mockContext: Context = mockk()

    private val testCorrelationId = "test-correlation-id"
    private val testCacheKey = "test-key"
    private val testCacheName = "test-cache"

    private fun createRequest(
        operation: RedisOperation,
        key: String = testCacheKey,
    ) = RedisRequest(
        correlationId = testCorrelationId,
        cacheKey = key,
        cacheName = testCacheName,
        operation = operation,
    )

    companion object {
        @JvmStatic
        fun cacheHitTestCases() =
            listOf(
                Arguments.of(RedisOperation.GET, Optional.of("cached-value"), true),
                Arguments.of(RedisOperation.GET, Optional.empty<String>(), false),
                Arguments.of(RedisOperation.GET, null, false),
                Arguments.of(RedisOperation.GET, "direct-value", true),
                Arguments.of(RedisOperation.PUT_IF_ABSENT, Optional.of("existing-value"), true),
                Arguments.of(RedisOperation.PUT_IF_ABSENT, Optional.empty<String>(), false),
            )

        @JvmStatic
        fun noCacheHitTestCases() =
            listOf(
                Arguments.of(RedisOperation.PUT),
                Arguments.of(RedisOperation.INVALIDATE),
                Arguments.of(RedisOperation.INVALIDATE_ALL),
            )
    }

    @BeforeEach
    fun setup() {
        clearMocks(mockAttributesBuilder, mockContext)
    }

    @Test
    fun `onStart should set basic cache attributes`() {
        val request = createRequest(RedisOperation.GET)

        extractor.onStart(mockAttributesBuilder, mockContext, request)

        verify { mockAttributesBuilder.put("cache.name", testCacheName) }
        verify { mockAttributesBuilder.put("cache.key", testCacheKey) }
        verify { mockAttributesBuilder.put("cache.operation", "GET") }
    }

    @Nested
    inner class OnEndTests {
        @ParameterizedTest(name = "[{index}] operation={0}, response={1}, expectedCacheHit={2}")
        @MethodSource("cz.pbktechnology.platform.common.context.instrument.RedisAttributesExtractorTest#cacheHitTestCases")
        fun `onEnd should set correct cache hit value for operations that track cache hits`(
            operation: RedisOperation,
            response: Any?,
            expectedCacheHit: Boolean,
        ) {
            val request = createRequest(operation)

            extractor.onEnd(mockAttributesBuilder, mockContext, request, response, null)

            verify { mockAttributesBuilder.put("cache.hit", expectedCacheHit) }
        }

        @ParameterizedTest(name = "[{index}] operation={0}")
        @MethodSource("cz.pbktechnology.platform.common.context.instrument.RedisAttributesExtractorTest#noCacheHitTestCases")
        fun `onEnd should not set cache hit for operations that do not track cache hits`(operation: RedisOperation) {
            val request = createRequest(operation, if (operation == RedisOperation.INVALIDATE_ALL) "*" else testCacheKey)

            extractor.onEnd(mockAttributesBuilder, mockContext, request, Unit, null)

            verify(exactly = 0) { mockAttributesBuilder.put("cache.hit", any<Boolean>()) }
        }
    }
}
