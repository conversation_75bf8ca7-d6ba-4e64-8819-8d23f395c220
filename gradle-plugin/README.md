# gradle-plugin

Contains two gradle plugins, `root` and `module`. `root` is meant to be applied in `/build.gradle.kts` and `module` in subrojects (
`/<module>/build.gradle.kts`).

## Pre commit hooks

By default, a pre commit that formats code is installed.
You can override this behaviour using `ktlint_pre_commit_hook` gradle property.
The available values are:

- `format` - Formats edited files. If there is an error that cannot be autocorrected it will abort the commit. This is the default.
- `check` - Checks the edited files. If there is any error it will abort the commit.
- `disable` - No pre commit hook is installed.

## Setup

> :warning: The minimum required version of gradle is 7.6.4. Run `./gradlew wrapper --gradle-version 7.6.4` to upgrade gradle.

```kotlin
// settings.gradle.kts
pluginManagement {
    val platformCommonVersion: String by settings

    val artifactoryUserProperty = settings.extra.takeIf { it.has("artifactory_user") }?.get("artifactory_user") as String?
    val artifactoryPasswordProperty = settings.extra.takeIf { it.has("artifactory_password") }?.get("artifactory_password") as String?
    val artifactoryUser = System.getenv("artifactory_user") ?: artifactoryUserProperty
    val artifactoryPassword = System.getenv("artifactory_password") ?: artifactoryPasswordProperty

    plugins {
        id("cz.pbktechnology.platform.common.gradle-plugin.root") version platformCommonVersion
        id("cz.pbktechnology.platform.common.gradle-plugin.module") version platformCommonVersion
    }

    repositories {
        maven {
            url = uri("https://artifactory.pbk-lab.tech/artifactory/gradle-plugins")
            credentials {
                username = artifactoryUser
                password = artifactoryPassword
            }
        }
        maven {
            url = uri("https://artifactory.pbk-lab.tech/artifactory/pbk")
            credentials {
                username = artifactoryUser
                password = artifactoryPassword
            }
        }
        mavenLocal()
    }
}
```

```kotlin
// build.gradle.kts
plugins {
    // ...
    id("cz.pbktechnology.platform.common.gradle-plugin.root")
}
```

```kotlin
// <module>/build.gradle.kts
plugins {
    // ...
    id("cz.pbktechnology.platform.common.gradle-plugin.module")
}
```

## Root gradle plugin

```kotlin
// build.gradle.kts
platform {

    // Sets up default maven repositories
    defaultRepositories {
        enabled = true // Optional with this default
        url = "https://artifactory.pbk-lab.tech/artifactory" // Optional with this default
    }

    // Sets up publishing to Artifactory
    publish {
        artifactoryUrl = "https://artifactory.pbk-lab.tech/artifactory" // Optional with this default
        repoKey = "pbk" // Optional with this default
        publishAllPublications = false // If non-pbk publications should be published. Optional with this default
    }

    ktlint {
        enabled = true // Optional with this default
        filter { // Optional with this default 
            exclude { element -> element.file.path.contains("${File.separatorChar}build${File.separatorChar}") }
        }
    }

    // Validates gateway.yaml for common anti-patterns
    gatewayYamlValidation {
        enabled = true // Optional with this default
    }
}
```

## Module gradle plugin

```kotlin
// <module>/build.gradle.kts
platform {
    excludeLog4j = true // Excludes log4j modules from dependencies. Optional with this default
    excludeSlf4jNop = true // Excludes slf4j-nop module from dependencies. Optional with this default

    // Sets up default maven repositories
    defaultRepositories {
        enabled = true // Optional with this default
        url = "https://artifactory.pbk-lab.tech/artifactory" // Optional with this default
    }

    ktlint {
        enabled = true // Optional with this default
        filter { // Optional with this default
            exclude { element -> element.file.path.contains("${File.separatorChar}build${File.separatorChar}") }
        }
    }

    // Sets up micronaut plugin, ENABLE THIS IN MICRONAUT APPLICATION
    micronaut {
        enabled = false // Optional with this default 
        mainClass = "cz.partners..." // If micronaut enabled it's required
        isHttpService = false // Optional with this default
        micronautVersion = "3.10.1" // Optional with this default

        // Registers task `generateInfoEndpointData` that generates properties file with data for info endpoint. 
        // Requires a `platformCommonVersion` gradle property.
        infoEndpointData {
            enabled = true // Optional with this default
            outputFilePath = "META-INF/build-info.properties" // Optional with this default
            additionalProperties = emptyMap<String, String>() // Optional with this default
        }

        configurationValidation {
            enabled = true // Optional with this default
            disableSchemas = listOf() // Disables checks for specified schemas - optional with this default 
        }
    }

    // Sets up JaCoCo plugin with code coverage report generation for Qodana
    jacoco {
        enabled = true // Optional with this default
    }

    // Sets up publishing to Artifactory for this module
    publishing {
        artifactId = "myArtifactId" // If present, enables the publishing
    }
}
```

## Onboarding already existing services

- Make sure your setup contains everything from the [setup step](#setup)
- Remove plugins from all `build.gradle.kts` and `settings.gradle.kts`
    - `kotlin("jvm")`
    - `kotlin("kapt")`
    - `id("org.jetbrains.kotlin.plugin.allopen")`
    - `id("com.github.johnrengelman.shadow")`
    - `id("io.micronaut.application")`
- Replace micronaut plugin configuration with
  ```kotlin
  platform {
    micronaut {
      enabled = true  
      isHttpService = ? // true in http services, false in db-init
      mainClass = "cz.partners..." 
    }
  }
  ```
- Remove `buildInfo` task
- Remove `java.sourceCompatibility`
- Remove `repositories` from all `build.gradle.kts`
- Remove `artifactory` from all `build.gradle.kts`
- Remove unused properties in `gradle.properties`
- Remove exclude of `org.apache.logging.log4j` group and `org.slf4j` group
- Remove `tasks.withType<KotlinCompile>` from all `build.gradle.kts`
- Remove `tasks.withType<JavaCompile>` from all `build.gradle.kts`
- If you use JaCoCo plugin, remove it from `build.gradle.kts`. If you want to use custom setup, you can disable platform JaCoCo
  instead
```kotlin
  platform {
    jacoco {
      enabled = false
    }
  }
```
