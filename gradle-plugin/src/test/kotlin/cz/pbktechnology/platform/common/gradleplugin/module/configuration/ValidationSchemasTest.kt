package cz.pbktechnology.platform.common.gradleplugin.module.configuration

import org.junit.jupiter.api.Test
import java.io.File

class ValidationSchemasTest {
    @Test
    fun `loggersConfigured schema`() {
        "loggersConfigured"
            .also {
                assert(isValid("loggersConfigured", it))
                assert(!isValid("loggersMisconfigured-1", it))
                assert(!isValid("loggersMisconfigured-2", it))
            }
    }

    @Test
    fun `otelDisabled schema`() {
        "otelDisabled"
            .also {
                assert(isValid("otelDisabled", it))
                assert(!isValid("otelEnabled", it))
            }
    }

    @Test
    fun `zipkinRemoved schema`() {
        "zipkinRemoved"
            .also {
                assert(isValid("zipkinRemoved", it))
                assert(!isValid("zipkinPresent", it))
            }
    }

    @Test
    fun `infoEndpointEnabled schema`() {
        "infoEndpointEnabled"
            .also {
                assert(isValid("infoEndpointEnabled", it))
                assert(!isValid("infoEndpointDisabled-1", it))
                assert(!isValid("infoEndpointDisabled-2", it))
            }
    }

    @Test
    fun `healthEndpointsConfigured schema`() {
        "healthEndpointConfigured"
            .also {
                assert(isValid("healthEndpointConfigured", it))
                assert(!isValid("healthEndpointMisconfigured-1", it))
                assert(!isValid("healthEndpointMisconfigured-2", it))
            }
    }

    @Test
    fun `redisVariablesConfigured schema`() {
        "redisVariablesConfigured"
            .also {
                assert(isValid("redisVariablesConfigured", it))
                assert(!isValid("redisVariablesMisconfigured-1", it))
                assert(!isValid("redisVariablesMisconfigured-2", it))
                assert(!isValid("redisVariablesMisconfigured-3", it))
                assert(!isValid("redisVariablesMisconfigured-4", it))
            }
    }

    @Test
    fun `datasourcesConfigured schema`() {
        "datasourcesConfigured"
            .also {
                assert(isValid("datasourcesConfigured-1", it))
                assert(isValid("datasourcesConfigured-2", it))
                assert(!isValid("datasourcesMisconfigured-1", it))
                assert(!isValid("datasourcesMisconfigured-2", it))
                assert(!isValid("datasourcesMisconfigured-3", it))
                assert(!isValid("datasourcesMisconfigured-4", it))
                assert(!isValid("datasourcesMisconfigured-5", it))
                assert(!isValid("datasourcesMisconfigured-6", it))
            }
    }

    @Test
    fun `userContextPropagationLegacyRemoved schema`() {
        "userContextPropagationLegacyRemoved"
            .also {
                assert(isValid("userContextPropagationLegacyRemoved", it))
                assert(!isValid("userContextPropagationLegacyPresent", it))
            }
    }

    @Test
    fun `redisCacheTimeoutConfigured schema`() {
        "redisCacheTimeoutConfigured".also {
            assert(isValid("redisCacheTimeoutRootConfigured", it))
            assert(isValid("redisCacheTimeoutRootNotNeeded", it))
            assert(!isValid("redisCacheTimeoutRootNotConfigured", it))

            assert(isValid("redisCacheTimeoutNamedConfigured", it))
            assert(isValid("redisCacheTimeoutNamedNotNeeded", it))
            assert(!isValid("redisCacheTimeoutNamedNotConfigured", it))
        }
    }

    private fun isValid(
        configurationName: String,
        schemaName: String,
    ): Boolean {
        val configuration =
            this.javaClass.classLoader
                .getResource("$CONFIGURATIONS_DIR_NAME/$configurationName.yaml")
                ?.file
                ?.let { File(it) } ?: error("Configuration file not found [$configurationName]")

        return runCatching {
            MicronautConfigurationValidator().also {
                it.validate(configuration, schemaName)
                it.throwIfErrors()
            }
        }.fold(
            onSuccess = { true },
            onFailure = {
                println(it)
                false
            },
        )
    }

    companion object {
        const val CONFIGURATIONS_DIR_NAME = "micronautConfigurations"
    }
}
