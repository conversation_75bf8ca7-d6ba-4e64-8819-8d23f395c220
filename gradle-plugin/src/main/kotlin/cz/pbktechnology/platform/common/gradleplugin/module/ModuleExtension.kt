package cz.pbktechnology.platform.common.gradleplugin.module

import cz.pbktechnology.platform.common.gradleplugin.common.Constants.INFO_ENDPOINT_DATA_OUTPUT_FILE_PATH
import cz.pbktechnology.platform.common.gradleplugin.common.DefaultRepositoriesExtension
import cz.pbktechnology.platform.common.gradleplugin.common.PlatformKtlintExtension
import org.gradle.api.Action

open class ModuleExtension(
    var excludeLog4j: Boolean = true,
    var excludeSlf4jNop: Boolean = true,
    var defaultRepositories: DefaultRepositoriesExtension = DefaultRepositoriesExtension(),
    var micronaut: PlatformMicronautExtension = PlatformMicronautExtension(),
    var publishing: PlatformPublishingExtension = PlatformPublishingExtension(),
    var ktlint: PlatformKtlintExtension = PlatformKtlintExtension(),
    var jacoco: PlatformJacocoExtension = PlatformJacocoExtension(),
) {
    fun defaultRepositories(action: Action<DefaultRepositoriesExtension>) {
        action.execute(defaultRepositories)
    }

    fun micronaut(action: Action<PlatformMicronautExtension>) {
        action.execute(micronaut)
    }

    fun publishing(action: Action<PlatformPublishingExtension>) {
        action.execute(publishing)
    }

    fun ktlint(action: Action<PlatformKtlintExtension>) {
        action.execute(ktlint)
    }

    fun jacoco(action: Action<PlatformJacocoExtension>) {
        action.execute(jacoco)
    }
}

open class PlatformMicronautExtension(
    var enabled: Boolean = false,
    var mainClass: String? = null,
    var isHttpService: Boolean = false,
    var infoEndpointData: InfoEndpointDataExtension = InfoEndpointDataExtension(),
    var configurationValidation: ConfigurationValidationExtension =
        ConfigurationValidationExtension(),
) {
    fun infoEndpointData(action: Action<InfoEndpointDataExtension>) {
        action.execute(infoEndpointData)
    }

    fun configurationValidation(action: Action<ConfigurationValidationExtension>) {
        action.execute(configurationValidation)
    }
}

open class ConfigurationValidationExtension(
    var enabled: Boolean = true,
    var disableSchemas: List<String> = emptyList(),
)

open class InfoEndpointDataExtension(
    var enabled: Boolean = true,
    /** Relative to resources dir */
    var outputFilePath: String = INFO_ENDPOINT_DATA_OUTPUT_FILE_PATH,
    var additionalProperties: Map<String, String> = emptyMap(),
)

open class PlatformPublishingExtension(
    var artifactId: String? = null,
)

open class PlatformJacocoExtension(
    var enabled: Boolean = true,
)
