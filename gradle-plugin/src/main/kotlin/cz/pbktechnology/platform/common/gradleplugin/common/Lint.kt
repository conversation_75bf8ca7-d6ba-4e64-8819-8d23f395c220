package cz.pbktechnology.platform.common.gradleplugin.common

import cz.pbktechnology.platform.common.gradleplugin.common.Constants.KTLINT_VERSION
import org.gradle.api.Project
import org.gradle.api.tasks.util.PatternFilterable
import org.gradle.api.tasks.util.PatternSet
import org.jlleitschuh.gradle.ktlint.KtlintExtension
import org.jlleitschuh.gradle.ktlint.KtlintPlugin

object Lint {
    fun configureKtlint(
        project: Project,
        extension: PlatformKtlintExtension,
    ) {
        if (!extension.enabled) return

        val filter = extension.filter

        project.plugins.apply(KtlintPlugin::class.java)
        project.extensions.configure(KtlintExtension::class.java) {
            it.verbose.set(true)
            it.filter {
                it.fromPatternSet(filter)
            }
            it.version.set(KTLINT_VERSION)
        }
    }

    private fun PatternFilterable.fromPatternSet(patternSet: PatternSet) {
        exclude(patternSet.excludes)
        patternSet.excludeSpecs.forEach { exclude(it) }
        include(patternSet.includes)
        patternSet.includeSpecs.forEach { include(it) }
    }
}
