package cz.pbktechnology.platform.common.gradleplugin.common

import org.gradle.api.JavaVersion

object Constants {
    const val MICRONAUT_VERSION = "3.10.1" // Duplicated in bom
    const val ARTIFACTORY_URL =
        "https://artifactory.pbk-lab.tech/artifactory" // Duplicated in `build.gradle.kts`
    const val PUBLISH_REPO_KEY = "pbk" // Duplicated in `build.gradle.kts`
    const val INFO_ENDPOINT_DATA_OUTPUT_FILE_PATH = "META-INF/build-info.properties"
    val JAVA_VERSION = JavaVersion.VERSION_11 // Duplicated in `build.gradle.kts`
    val ANNOTATION_PACKAGES = arrayOf("cz.partners.*", "cz.pbktechnology.*")
    const val ALL_OPEN_NAMES = "javax.persistence.Entity"
    const val PBK_PUBLICATION_NAME = "pbkPublication"
    const val ALL_PUBLICATION_NAME = "ALL_PUBLICATIONS"
    const val KTLINT_VERSION = "1.3.1" // Duplicated in `build.gradle.kts`
    const val KTLINT_PRE_COMMIT_HOOK_PROPERTY = "ktlint_pre_commit_hook"
    const val JACOCO_VERSION = "0.8.8"
}
