package cz.pbktechnology.platform.common.gradleplugin.module.configuration

import cz.pbktechnology.platform.common.gradleplugin.module.ModuleExtension
import org.gradle.api.DefaultTask
import org.gradle.api.plugins.JavaPluginExtension
import org.gradle.api.tasks.TaskAction

abstract class ValidateMicronautConfigurationTask : DefaultTask() {
    private val validator = MicronautConfigurationValidator()
    private val moduleExtension = project.extensions.getByType(ModuleExtension::class.java)
    private val disableSchemas =
        moduleExtension.micronaut.configurationValidation.disableSchemas
            .toMutableList()

    @TaskAction
    fun action() {
        validate("main", "zipkinRemoved", onlyMainApplicationYaml = false)
        validate("main", "userContextPropagationLegacyRemoved", onlyMainApplicationYaml = false)
        validate("main", "redisVariablesConfigured", onlyMainApplicationYaml = false)
        validate("main", "redisCacheTimeoutConfigured", onlyMainApplicationYaml = false)

        if (moduleExtension.micronaut.isHttpService) {
            validate("main", "infoEndpointEnabled", onlyMainApplicationYaml = true)
            validate("main", "healthEndpointConfigured", onlyMainApplicationYaml = true)
        }

        if (project.name != "db-init") {
            validate("main", "loggersConfigured", onlyMainApplicationYaml = true)
        }

        validate("test", "otelDisabled", onlyMainApplicationYaml = true)

        if (disableSchemas.isNotEmpty()) {
            val disableSchemasJoined = disableSchemas.joinToString(", ")
            error(
                "Invalid values [$disableSchemasJoined] in micronaut.configurationValidation.disableSchemas",
            )
        }

        validator.throwIfErrors()
    }

    private fun validate(
        sourceSet: String,
        schemaName: String,
        onlyMainApplicationYaml: Boolean,
    ) {
        if (disableSchemas.contains(schemaName)) {
            disableSchemas.removeIf { it == schemaName }
            return
        }

        val javaPluginExtension = project.extensions.getByType(JavaPluginExtension::class.java)

        val fileNamePattern =
            when (onlyMainApplicationYaml) {
                true -> "^application\\.ya?ml$"
                false -> "^application(-.+)?\\.ya?ml$"
            }
        javaPluginExtension.sourceSets.getByName(sourceSet).resources.srcDirs.forEach { resourcesDir ->
            resourcesDir
                .listFiles { dir, name -> Regex(fileNamePattern).matches(name) }
                ?.forEach { validator.validate(it, schemaName) }
        }
    }
}
