package cz.pbktechnology.platform.common.gradleplugin.module

import cz.pbktechnology.platform.common.gradleplugin.common.Constants.ALL_OPEN_NAMES
import cz.pbktechnology.platform.common.gradleplugin.common.Constants.ANNOTATION_PACKAGES
import cz.pbktechnology.platform.common.gradleplugin.common.Constants.JACOCO_VERSION
import cz.pbktechnology.platform.common.gradleplugin.common.Constants.JAVA_VERSION
import cz.pbktechnology.platform.common.gradleplugin.common.Constants.MICRONAUT_VERSION
import cz.pbktechnology.platform.common.gradleplugin.common.Constants.PBK_PUBLICATION_NAME
import cz.pbktechnology.platform.common.gradleplugin.common.Lint
import cz.pbktechnology.platform.common.gradleplugin.common.Repositories
import cz.pbktechnology.platform.common.gradleplugin.module.configuration.ValidateMicronautConfigurationTask
import io.micronaut.gradle.MicronautApplicationPlugin
import io.micronaut.gradle.MicronautExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.plugins.ApplicationPlugin
import org.gradle.api.plugins.JavaApplication
import org.gradle.api.plugins.JavaPlatformPlugin
import org.gradle.api.plugins.JavaPlugin
import org.gradle.api.plugins.JavaPluginExtension
import org.gradle.api.publish.PublishingExtension
import org.gradle.api.publish.maven.MavenPublication
import org.gradle.api.publish.maven.plugins.MavenPublishPlugin
import org.gradle.api.tasks.compile.JavaCompile
import org.gradle.api.tasks.testing.Test
import org.gradle.plugin.devel.plugins.JavaGradlePluginPlugin
import org.gradle.testing.jacoco.plugins.JacocoPlugin
import org.gradle.testing.jacoco.plugins.JacocoPluginExtension
import org.gradle.testing.jacoco.tasks.JacocoReport
import org.jetbrains.kotlin.allopen.gradle.AllOpenExtension
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.jfrog.gradle.plugin.artifactory.ArtifactoryPlugin

class ModuleGradlePlugin : Plugin<Project> {
    override fun apply(project: Project) {
        val extension = project.extensions.create("platform", ModuleExtension::class.java)

        applySimplePlugins(project)
        applyKotlinPlugin(project)
        applyJavaPlugin(project)
        applyPublishingPlugins(project)
        applyAllOpenPlugin(project)

        // This ensures that extensions are loaded with users data
        project.afterEvaluate {
            Repositories.setup(project, extension.defaultRepositories)
            setupMicronaut(project)
            setupPublishing(project)
            configureKtlint(project)
            excludeLog4j(project)
            excludeSlf4jNop(project)
            configureJaCoCo(project)
        }
    }

    private fun applySimplePlugins(project: Project) {
        project.plugins.apply("org.jetbrains.kotlin.plugin.jpa")
    }

    private fun applyJavaPlugin(project: Project) {
        // java-platform is incompatible with java
        if (project.plugins.findPlugin(JavaPlatformPlugin::class.java) != null) return
        project.plugins.apply(JavaPlugin::class.java)

        project.extensions.configure(JavaPluginExtension::class.java) {
            it.sourceCompatibility = JAVA_VERSION
            it.withSourcesJar()
        }

        project.tasks.withType(JavaCompile::class.java).forEach { it.options.encoding = "UTF-8" }
    }

    private fun applyKotlinPlugin(project: Project) {
        // java-platform is incompatible with java
        if (project.plugins.findPlugin(JavaPlatformPlugin::class.java) != null) return

        project.plugins.apply("org.jetbrains.kotlin.jvm")
        project.plugins.apply("org.jetbrains.kotlin.kapt")
        project.tasks.withType(KotlinCompile::class.java) {
            it.kotlinOptions.jvmTarget = JAVA_VERSION.toString()
        }
    }

    private fun setupMicronaut(project: Project) {
        val moduleExtension = project.extensions.getByType(ModuleExtension::class.java)
        if (!moduleExtension.micronaut.enabled) return

        val config = moduleExtension.micronaut
        val mainClass = config.mainClass ?: error("platform.micronaut.mainClass is required")

        project.plugins.apply(MicronautApplicationPlugin::class.java)
        project.plugins.apply(ApplicationPlugin::class.java)

        project.extensions.configure(MicronautExtension::class.java) {
            it.version(MICRONAUT_VERSION)
            it.testRuntime("junit5")
            it.processing {
                it.incremental(true)
                it.annotations(*ANNOTATION_PACKAGES)
            }

            if (config.isHttpService) it.runtime("netty")
        }

        project.extensions.configure(JavaApplication::class.java) { it.mainClass.set(mainClass) }

        project.tasks.withType(JavaCompile::class.java).forEach {
            it.options.compilerArgs.addAll(
                listOf(
                    "-parameters",
                    "-Amicronaut.processing.incremental=true",
                    "-Amicronaut.processing.annotations=${ANNOTATION_PACKAGES.joinToString(",")}",
                    "-Amicronaut.processing.group=${it.group}",
                    "-Amicronaut.processing.module=${it.name}",
                ),
            )
        }

        if (config.configurationValidation.enabled) {
            val validateMicronautConfigurationTask =
                project.tasks
                    .create(
                        "validateMicronautConfiguration",
                        ValidateMicronautConfigurationTask::class.java,
                    ).apply { group = "verification" }
            project.tasks.getByName("classes").dependsOn(validateMicronautConfigurationTask)
        }

        if (config.infoEndpointData.enabled) {
            val generateInfoEndpointDataTask =
                project.tasks
                    .create("generateInfoEndpointData", InfoEndpointDataTask::class.java)
                    .apply {
                        group = "build"
                        // To always run
                        outputs.upToDateWhen { false }
                        // Without this the generated file might become stale and gradle will delete it
                        outputs.file(InfoEndpointDataTask.getOutputFilePath(project))
                    }

            project.tasks.getByName("classes").dependsOn(generateInfoEndpointDataTask)
        }
    }

    private fun applyAllOpenPlugin(project: Project) {
        project.plugins.apply("org.jetbrains.kotlin.plugin.allopen")

        project.extensions.configure(AllOpenExtension::class.java) {
            it.annotations(ALL_OPEN_NAMES)
        }
    }

    /**
     * For some reason, that the gods of gradle don't want anyone to know, this needs to run before
     * `afterEvaluate`
     */
    private fun applyPublishingPlugins(project: Project) {
        project.plugins.apply(MavenPublishPlugin::class.java)
        project.plugins.apply(ArtifactoryPlugin::class.java)
    }

    private fun setupPublishing(project: Project) {
        val moduleExtensions = project.extensions.getByType(ModuleExtension::class.java)

        val artifactId = moduleExtensions.publishing.artifactId ?: return

        project.extensions.configure(PublishingExtension::class.java) {
            it.publications {
                it.register(PBK_PUBLICATION_NAME, MavenPublication::class.java) {
                    if (project.plugins.findPlugin(JavaPlugin::class.java) != null ||
                        project.plugins.findPlugin(JavaGradlePluginPlugin::class.java) != null
                    ) {
                        it.from(project.components.getByName("java"))
                    } else if (project.plugins.findPlugin(JavaPlatformPlugin::class.java) != null) {
                        it.from(project.components.getByName("javaPlatform"))
                    } else {
                        error("One of these plugins needs to be registered [java, java-plugin]")
                    }

                    it.artifactId = artifactId
                }
            }
        }
    }

    /**
     * Sometimes gets included from Micronaut's dependencies
     */
    private fun excludeLog4j(project: Project) {
        val moduleExtensions = project.extensions.getByType(ModuleExtension::class.java)
        if (!moduleExtensions.excludeLog4j) return

        project.configurations.all {
            it.exclude(mapOf("group" to "org.apache.logging.log4j", "module" to "log4j-core"))
            it.exclude(mapOf("group" to "org.apache.logging.log4j", "module" to "log4j-api"))
            it.exclude(mapOf("group" to "org.apache.logging.log4j", "module" to "log4j-slf4j-impl"))
        }
    }

    /**
     * Sometimes gets included from Micronaut's dependencies and breaks logging
     */
    private fun excludeSlf4jNop(project: Project) {
        val moduleExtensions = project.extensions.getByType(ModuleExtension::class.java)
        if (!moduleExtensions.excludeSlf4jNop) return

        project.configurations.all {
            it.exclude(mapOf("group" to "org.slf4j", "module" to "slf4j-nop"))
        }
    }

    private fun configureKtlint(project: Project) {
        val moduleExtension = project.extensions.getByType(ModuleExtension::class.java)

        Lint.configureKtlint(project, moduleExtension.ktlint)
    }

    private fun configureJaCoCo(project: Project) {
        val moduleExtensions = project.extensions.getByType(ModuleExtension::class.java)
        if (!moduleExtensions.jacoco.enabled) return
        if (project.plugins.findPlugin(JavaPlatformPlugin::class.java) != null) return

        if (project.plugins.hasPlugin(JacocoPlugin::class.java)) {
            error(
                "Duplicate Jacoco plugin detected. Either remove jacoco plugin from build.gradle.kts to use platform " +
                    "one or disable platform jacoco module " +
                    "(https://gitlab.pbk-lab.tech/banka/shared/platform/platform-common/-/tree/dev/gradle-plugin?ref_type=heads)",
            )
        }

        project.plugins.apply(JacocoPlugin::class.java)

        project.extensions.configure(JacocoPluginExtension::class.java) {
            it.toolVersion = JACOCO_VERSION
        }

        project.tasks.withType(Test::class.java) {
            it.finalizedBy("jacocoTestReport")
        }

        project.tasks.withType(JacocoReport::class.java) {
            it.reports.xml.required
                .set(true)
            it.reports.html.required
                .set(true)
        }
    }
}
