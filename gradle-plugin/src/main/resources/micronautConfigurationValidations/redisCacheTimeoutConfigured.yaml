$schema: https://json-schema.org/draft/2020-12/schema
description: We need to configure redis cache timeout to avoid long timeouts when it's offline.
  We can't validate Redis servers independently, so feel free to ignore this validation if you have more servers configured.
type: object
properties:
  redis:
    type: object
    properties:
      caches:
        type: object
      timeout:
        type: string

    dependentSchemas:
      caches:
        type: object
        properties:
          servers:
            type: object
            additionalProperties:
              type: object
              properties:
                timeout:
                  type: string
              required:
                - timeout
        required:
          - timeout