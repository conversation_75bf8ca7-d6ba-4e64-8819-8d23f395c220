package cz.pbktechnology.platform.common.redislettuce.cache

import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.OperationContext
import cz.pbktechnology.platform.common.context.instrument.RedisRequest
import io.lettuce.core.api.StatefulConnection
import io.micronaut.cache.SyncCache
import io.micronaut.core.type.Argument
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.opentelemetry.context.Context
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.Optional
import java.util.UUID
import java.util.function.Supplier

class InstrumentedRedisCacheUnitTest {
    private val mockDelegate: SyncCache<StatefulConnection<ByteArray, ByteArray>> = mockk()
    private val mockInstrumenter: Instrumenter<RedisRequest, Any> = mockk()
    private val instrumentedCache = InstrumentedRedisCache(mockInstrumenter)
    private val wrappedCache = instrumentedCache.wrap(mockDelegate)

    private val testCacheName = "test-cache"
    private val testKey = "test-key"
    private val testValue = "test-value"

    @BeforeEach
    fun setup() {
        clearMocks(mockDelegate, mockInstrumenter)
        every { mockDelegate.name } returns testCacheName
        every { mockDelegate.nativeCache } returns mockk()
    }

    @Test
    fun `should wrap cache with instrumentation when instrumenter is available`() {
        val cache = instrumentedCache.wrap(mockDelegate)
        assertThat(cache).isNotSameAs(mockDelegate)
        assertThat(cache.name).isEqualTo(testCacheName)
    }

    @Test
    fun `should return original cache when instrumenter is null`() {
        val instrumentedCacheWithNullInstrumenter = InstrumentedRedisCache(null)
        val cache = instrumentedCacheWithNullInstrumenter.wrap(mockDelegate)
        assertThat(cache).isSameAs(mockDelegate)
    }

    @Test
    fun `should create span for cache operations`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val correlationId = UUID.randomUUID()
            ContextConfiguration.operationContext.set(OperationContext(correlationId))

            val mockContext = mockk<Context>()
            val mockScope = mockk<io.opentelemetry.context.Scope>()
            val requestSlot = slot<RedisRequest>()

            every { mockInstrumenter.start(any(), capture(requestSlot)) } returns mockContext
            every { mockContext.makeCurrent() } returns mockScope
            every { mockScope.close() } returns Unit
            every { mockInstrumenter.end(mockContext, any(), any(), null) } returns Unit
            every { mockDelegate.invalidate(testKey) } returns Unit

            wrappedCache.invalidate(testKey)

            verify { mockInstrumenter.start(any(), any()) }
            verify { mockInstrumenter.end(mockContext, any(), Unit, null) }
            verify { mockScope.close() }

            val capturedRequest = requestSlot.captured
            assertThat(capturedRequest.correlationId).isEqualTo(correlationId.toString())
            assertThat(capturedRequest.cacheName).isEqualTo(testCacheName)
        }

    @Test
    fun `should delegate cache operations to underlying cache implementation`() {
        val argument = Argument.of(String::class.java)
        val supplier = Supplier { "supplied-value" }

        val mockContext = mockk<Context>()
        val mockScope = mockk<io.opentelemetry.context.Scope>()

        every { mockInstrumenter.start(any(), any()) } returns mockContext
        every { mockContext.makeCurrent() } returns mockScope
        every { mockScope.close() } returns Unit
        every { mockInstrumenter.end(mockContext, any(), any(), null) } returns Unit

        every { mockDelegate.get(testKey, argument) } returns Optional.of(testValue)
        every { mockDelegate.get(testKey, argument, supplier) } returns "value-with-supplier"
        every { mockDelegate.putIfAbsent(testKey, testValue) } returns Optional.empty()

        assertThat(wrappedCache.get(testKey, argument)).isEqualTo(Optional.of(testValue))
        assertThat(wrappedCache.get(testKey, argument, supplier)).isEqualTo("value-with-supplier")
        assertThat(wrappedCache.putIfAbsent(testKey, testValue)).isEqualTo(Optional.empty<String>())

        // Verify delegate operations were called
        verify { mockDelegate.get(testKey, argument) }
        verify { mockDelegate.get(testKey, argument, supplier) }
        verify { mockDelegate.putIfAbsent(testKey, testValue) }

        // Verify instrumentation was called for each operation
        verify(exactly = 3) { mockInstrumenter.start(any(), any()) }
        verify(exactly = 3) { mockInstrumenter.end(mockContext, any(), any(), null) }
        verify(exactly = 3) { mockScope.close() }
    }
}
