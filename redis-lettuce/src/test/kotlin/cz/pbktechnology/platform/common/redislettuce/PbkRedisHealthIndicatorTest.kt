package cz.pbktechnology.platform.common.redislettuce

import eu.rekawek.toxiproxy.ToxiproxyClient
import io.micronaut.health.HealthStatus
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.micronaut.test.support.TestPropertyProvider
import jakarta.inject.Inject
import kotlinx.coroutines.reactive.awaitSingle
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.ToxiproxyContainer
import org.testcontainers.junit.jupiter.Container
import kotlin.test.Test
import kotlin.test.assertEquals

class PbkRedisHealthIndicatorTest {
    @AfterEach
    fun reset() {
        defaultServerProxy.enable()
        namedServerProxy.enable()
    }

    @Nested
    @MicronautTest
    @TestInstance(TestInstance.Lifecycle.PER_CLASS) // Required for `TestPropertyProvider` to work
    inner class DefaultServerEnabled : TestPropertyProvider {
        override fun getProperties() =
            mapOf(
                "redis.host" to toxiproxyContainer.host,
                "redis.port" to toxiproxyContainer.getMappedPort(DEFAULT_SERVER_PROXY_PORT).toString(),
            )

        @Inject
        private lateinit var healthIndicator: PbkRedisHealthIndicator

        @Test
        fun `getResult - returns UP - when Redis connected`() =
            runBlocking {
                val result = healthIndicator.result.awaitSingle()
                assertEquals(expected = HealthStatus.UP, result.status)
            }

        @Test
        fun `getResult - returns DOWN - when Redis disconnected`() =
            runBlocking {
                defaultServerProxy.disable()

                val result = healthIndicator.result.awaitSingle()
                assertEquals(expected = HealthStatus.DOWN, actual = result.status)
            }
    }

    @Nested
    @MicronautTest
    @TestInstance(TestInstance.Lifecycle.PER_CLASS) // Required for `TestPropertyProvider` to work
    inner class DefaultServerDisabled : TestPropertyProvider {
        override fun getProperties() =
            mapOf(
                "redis.host" to toxiproxyContainer.host,
                "redis.port" to toxiproxyContainer.getMappedPort(DEFAULT_SERVER_PROXY_PORT).toString(),
                "redis.default-server-health-indicator-enabled" to "false",
                "redis.servers.named.host" to toxiproxyContainer.host,
                "redis.servers.named.port" to toxiproxyContainer.getMappedPort(NAMED_SERVER_PROXY_PORT).toString(),
            )

        @Inject
        private lateinit var healthIndicator: PbkRedisHealthIndicator

        @Test
        fun `getResult - returns UP - when Redis connected`() =
            runBlocking {
                val result = healthIndicator.result.awaitSingle()
                assertEquals(expected = HealthStatus.UP, actual = result.status)
            }

        @Test
        fun `getResult - returns UP - when Redis disconnected`() =
            runBlocking {
                defaultServerProxy.disable()

                val result = healthIndicator.result.awaitSingle()
                assertEquals(expected = HealthStatus.UP, actual = result.status)
            }
    }

    @Nested
    @MicronautTest
    @TestInstance(TestInstance.Lifecycle.PER_CLASS) // Required for `TestPropertyProvider` to work
    inner class NamedServerEnabled : TestPropertyProvider {
        override fun getProperties() =
            mapOf(
                "redis.host" to toxiproxyContainer.host,
                "redis.port" to toxiproxyContainer.getMappedPort(DEFAULT_SERVER_PROXY_PORT).toString(),
                "redis.servers.named.host" to toxiproxyContainer.host,
                "redis.servers.named.port" to toxiproxyContainer.getMappedPort(NAMED_SERVER_PROXY_PORT).toString(),
                "redis.servers.named.health-indicator-enabled" to "false",
            )

        @Inject
        private lateinit var healthIndicator: PbkRedisHealthIndicator

        @Test
        fun `getResult - returns UP - when Redis connected`() =
            runBlocking {
                val result = healthIndicator.result.awaitSingle()
                assertEquals(expected = HealthStatus.UP, actual = result.status)
            }

        @Test
        fun `getResult - returns UP - when Redis disconnected`() =
            runBlocking {
                namedServerProxy.disable()

                val result = healthIndicator.result.awaitSingle()
                assertEquals(expected = HealthStatus.UP, actual = result.status)
            }
    }

    @Nested
    @MicronautTest
    @TestInstance(TestInstance.Lifecycle.PER_CLASS) // Required for `TestPropertyProvider` to work
    inner class NamedServerDisabled : TestPropertyProvider {
        override fun getProperties() =
            mapOf(
                "redis.host" to toxiproxyContainer.host,
                "redis.port" to toxiproxyContainer.getMappedPort(DEFAULT_SERVER_PROXY_PORT).toString(),
                "redis.servers.named.host" to toxiproxyContainer.host,
                "redis.servers.named.port" to toxiproxyContainer.getMappedPort(NAMED_SERVER_PROXY_PORT).toString(),
            )

        @Inject
        private lateinit var healthIndicator: PbkRedisHealthIndicator

        @Test
        fun `getResult - returns UP - when Redis connected`() =
            runBlocking {
                val result = healthIndicator.result.awaitSingle()
                assertEquals(expected = HealthStatus.UP, actual = result.status)
            }

        @Test
        fun `getResult - returns DOWN - when Redis disconnected`() =
            runBlocking {
                namedServerProxy.disable()

                val result = healthIndicator.result.awaitSingle()
                assertEquals(expected = HealthStatus.DOWN, actual = result.status)
            }
    }

    companion object {
        private const val DEFAULT_SERVER_PROXY_PORT = 8666
        private const val NAMED_SERVER_PROXY_PORT = 8667

        private val network = Network.newNetwork()

        @Container
        private val redisTestContainer =
            GenericContainer(REDIS_IMAGE_NAME)
                .withExposedPorts(REDIS_PORT)
                .withNetwork(network)
                .withNetworkAliases("redis")
                .also { it.start() }

        @Container
        private val toxiproxyContainer =
            ToxiproxyContainer(TOXIPROXY_IMAGE_NAME).withNetwork(network).also {
                it.start()
            }

        private val toxiproxyClient =
            ToxiproxyClient(toxiproxyContainer.host, toxiproxyContainer.controlPort)
        private val defaultServerProxy =
            toxiproxyClient.createProxy("default-server", "0.0.0.0:$DEFAULT_SERVER_PROXY_PORT", "redis:${REDIS_PORT}")
        private val namedServerProxy =
            toxiproxyClient.createProxy("named-server", "0.0.0.0:$NAMED_SERVER_PROXY_PORT", "redis:${REDIS_PORT}")
    }
}
