package cz.pbktechnology.platform.common.redislettuce.cache

import cz.pbktechnology.platform.common.redislettuce.REDIS_IMAGE_NAME
import cz.pbktechnology.platform.common.redislettuce.REDIS_PORT
import cz.pbktechnology.platform.common.redislettuce.RedisConfiguration
import cz.pbktechnology.platform.common.redislettuce.TOXIPROXY_IMAGE_NAME
import eu.rekawek.toxiproxy.ToxiproxyClient
import io.github.resilience4j.retry.RetryRegistry
import io.micronaut.configuration.lettuce.cache.DefaultRedisCacheConfiguration
import io.micronaut.configuration.lettuce.cache.RedisCacheConfiguration
import io.micronaut.context.BeanLocator
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.core.convert.ConversionService
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.micronaut.test.support.TestPropertyProvider
import jakarta.inject.Inject
import jakarta.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.ToxiproxyContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

@Testcontainers
@MicronautTest(environments = ["CacheStartupConnectionTest"])
@TestInstance(TestInstance.Lifecycle.PER_CLASS) // Required for `TestPropertyProvider` to work
class CacheStartupConnectionTest : TestPropertyProvider {
    override fun getProperties() =
        mapOf(
            "redis.host" to toxiproxyContainer.host,
            "redis.port" to toxiproxyContainer.getMappedPort(PROXY_PORT).toString(),
            "redis.caches.test" to "",
        )

    @Inject
    lateinit var beanLocator: BeanLocator

    @Test
    fun `Connects on first try when proxy enabled`() {
        val startTime = LocalDateTime.now()

        beanLocator.getBean(PbkRedisCache::class.java)
        val connectTime = LocalDateTime.now()

        val didConnectOnFirstTry =
            startTime.plus(1.seconds.toJavaDuration()).isAfter(connectTime)
        assert(didConnectOnFirstTry)
    }

    @Test
    fun `Connection fails, tries to retry few times and finally connects when proxy is enabled`() =
        // Without specifying the dispatcher the coroutines don't run in parallel
        runBlocking(Dispatchers.Default) {
            val startTime = LocalDateTime.now()
            val connectAfter = 5.seconds
            proxy.disable()

            launch {
                delay(connectAfter)
                proxy.enable()
            }

            launch {
                beanLocator.getBean(PbkRedisCache::class.java)
                assertConnectAfterProxyEnabled(startTime, connectAfter)
            }
            Unit
        }

    /**
     * The [PbkRedisCache] is created on startup which prevents it to be testable.
     * This singleton is created after `beanLocator.getBean` call.
     */
    @Singleton
    @Replaces(PbkRedisCache::class)
    @Requires(env = ["CacheStartupConnectionTest"])
    class PbkRedisCacheCreatedOnDemand(
        defaultRedisCacheConfiguration: DefaultRedisCacheConfiguration,
        redisCacheConfiguration: RedisCacheConfiguration,
        redisCacheAdditionalConfigurations: List<RedisCacheAdditionalConfiguration>,
        conversionService: ConversionService<*>,
        beanLocator: BeanLocator,
        retryRegistry: RetryRegistry,
    ) : PbkRedisCache(
            defaultRedisCacheConfiguration,
            redisCacheConfiguration,
            redisCacheAdditionalConfigurations,
            conversionService,
            beanLocator,
            RedisConfiguration().apply {
                initConnectionRetryDelay =
                    RETRY_DELAY
            },
            retryRegistry,
        )

    private fun assertConnectAfterProxyEnabled(
        startTime: LocalDateTime,
        connectAfter: Duration,
    ) {
        val connectTime = LocalDateTime.now()
        val didConnectAfterProxyEnable =
            startTime.plus(connectAfter.toJavaDuration()).isBefore(connectTime)
        assert(didConnectAfterProxyEnable)
    }

    companion object {
        private val RETRY_DELAY = 1.seconds.toJavaDuration()

        private const val PROXY_PORT = 8666

        private val network = Network.newNetwork()

        @Container
        private val redisTestContainer =
            GenericContainer(REDIS_IMAGE_NAME)
                .withExposedPorts(REDIS_PORT)
                .withNetwork(network)
                .withNetworkAliases("redis")
                .also { it.start() }

        @Container
        private val toxiproxyContainer =
            ToxiproxyContainer(TOXIPROXY_IMAGE_NAME).withNetwork(network).also {
                it.start()
            }

        private val toxiproxyClient =
            ToxiproxyClient(toxiproxyContainer.host, toxiproxyContainer.controlPort)
        private val proxy =
            toxiproxyClient.createProxy("redis", "0.0.0.0:$PROXY_PORT", "redis:${REDIS_PORT}")
    }
}
