package cz.pbktechnology.platform.common.redislettuce

import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.lettuce.core.RedisClient
import io.lettuce.core.api.StatefulRedisConnection
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection
import io.micronaut.configuration.lettuce.AbstractRedisClientFactory
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import java.time.Duration

@Singleton
class BaseRedisClientFactory(
    @Value("\${redis.init-connection-retry-delay:5s}") val retryDelay: Duration,
    retryRegistry: RetryRegistry,
) : AbstractRedisClientFactory() {
    private val logger = LoggerFactory.getLogger(javaClass)

    private val retryConfig =
        RetryConfig
            .custom<Any>()
            .maxAttempts(Int.MAX_VALUE)
            .retryOnException {
                logger.error(
                    "Failed to connect, will retry in [${retryDelay.toSeconds()}]s",
                    it,
                )
                true
            }.waitDuration(retryDelay)
            .build()

    private val connectionRetry = retryRegistry.retry("${this::class.qualifiedName}:connection", retryConfig)
    private val pubsSubConnectionRetry =
        retryRegistry.retry("${this::class.qualifiedName}:pubsSubConnectionRetry", retryConfig)

    override fun redisConnection(redisClient: RedisClient): StatefulRedisConnection<String, String> =
        connectionRetry.executeCallable { super.redisConnection(redisClient) }

    override fun redisPubSubConnection(redisClient: RedisClient): StatefulRedisPubSubConnection<String, String> =
        pubsSubConnectionRetry.executeCallable {
            super.redisPubSubConnection(redisClient)
        }
}
