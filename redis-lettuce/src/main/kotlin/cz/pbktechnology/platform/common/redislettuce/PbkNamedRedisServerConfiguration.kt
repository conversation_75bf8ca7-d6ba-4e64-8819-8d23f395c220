package cz.pbktechnology.platform.common.redislettuce

import io.micronaut.configuration.lettuce.AbstractRedisConfiguration
import io.micronaut.configuration.lettuce.RedisSetting
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter

/**
 * [AbstractRedisConfiguration] + additional properties
 */
@EachProperty(RedisSetting.REDIS_SERVERS)
class PbkNamedRedisServerConfiguration(
    @Parameter name: String,
) : AbstractRedisConfiguration() {
    init {
        setName(name)
    }

    var enabled = true
    var healthIndicatorEnabled = true
}
