package cz.pbktechnology.platform.common.redislettuce.cache

import cz.pbktechnology.platform.common.redislettuce.RedisConfiguration
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.lettuce.core.api.StatefulConnection
import io.micronaut.cache.SyncCache
import io.micronaut.configuration.lettuce.cache.DefaultRedisCacheConfiguration
import io.micronaut.configuration.lettuce.cache.RedisCache
import io.micronaut.configuration.lettuce.cache.RedisCacheConfiguration
import io.micronaut.context.BeanLocator
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.exceptions.ConfigurationException
import io.micronaut.core.convert.ConversionService
import io.micronaut.core.type.Argument
import org.slf4j.LoggerFactory
import java.util.Optional
import java.util.function.Supplier

/**
 * - Adds retries on startup when Redis is not available when using caches
 * - Can disable caching by configuration
 */
@EachBean(RedisCacheConfiguration::class)
@Replaces(RedisCache::class)
open class PbkRedisCache(
    defaultRedisCacheConfiguration: DefaultRedisCacheConfiguration,
    redisCacheConfiguration: RedisCacheConfiguration,
    redisCacheAdditionalConfigurations: List<RedisCacheAdditionalConfiguration>,
    conversionService: ConversionService<*>,
    private val beanLocator: BeanLocator,
    private val redisConfiguration: RedisConfiguration,
    retryRegistry: RetryRegistry,
) : SyncCache<StatefulConnection<ByteArray, ByteArray>> {
    private val logger = LoggerFactory.getLogger(javaClass)

    private val retryCreateRedisCache =
        retryRegistry.retry(
            "${this::class.qualifiedName}:createRedisCache",
            RetryConfig
                .custom<Any>()
                .maxAttempts(Int.MAX_VALUE)
                .retryOnException {
                    if (
                        it is ConfigurationException
                    ) {
                        // Retry when configuration is invalid won't help
                        false
                    } else {
                        logger.error(
                            "Failed to connect, will retry in [${redisConfiguration.initConnectionRetryDelay.toSeconds()}]s",
                            it,
                        )
                        true
                    }
                }.waitDuration(redisConfiguration.initConnectionRetryDelay)
                .build(),
        )

    private val isEnabled =
        redisCacheAdditionalConfigurations
            .find { it.cacheName == redisCacheConfiguration.cacheName }
            ?.enabled
            ?: error("Cache configuration not found for cache [${redisCacheConfiguration.cacheName}]")
    private val redisCache: SyncCache<StatefulConnection<ByteArray, ByteArray>> =
        if (isEnabled) {
            val baseCache =
                retryCreateRedisCache.executeCallable {
                    RedisCache(defaultRedisCacheConfiguration, redisCacheConfiguration, conversionService, beanLocator)
                }
            beanLocator
                .findBean(InstrumentedRedisCache::class.java)
                .map { it.wrap(baseCache) }
                .orElse(baseCache)
        } else {
            DisabledRedisCache(redisCacheConfiguration)
        }

    override fun getName(): String = redisCache.name

    override fun getNativeCache(): StatefulConnection<ByteArray, ByteArray> = redisCache.nativeCache

    override fun <T : Any?> get(
        key: Any,
        requiredType: Argument<T>,
    ): Optional<T> = redisCache.get(key, requiredType)

    override fun <T : Any?> get(
        key: Any,
        requiredType: Argument<T>,
        supplier: Supplier<T>,
    ): T = redisCache.get(key, requiredType, supplier)

    override fun <T : Any?> putIfAbsent(
        key: Any,
        value: T,
    ): Optional<T> = redisCache.putIfAbsent(key, value)

    override fun put(
        key: Any,
        value: Any,
    ) = redisCache.put(key, value)

    override fun invalidate(key: Any) = redisCache.invalidate(key)

    override fun invalidateAll() = redisCache.invalidateAll()
}
