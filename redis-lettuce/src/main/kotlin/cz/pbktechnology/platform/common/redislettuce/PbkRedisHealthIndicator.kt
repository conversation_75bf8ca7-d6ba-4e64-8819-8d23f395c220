package cz.pbktechnology.platform.common.redislettuce

import io.lettuce.core.RedisClient
import io.lettuce.core.cluster.RedisClusterClient
import io.micronaut.configuration.lettuce.health.RedisHealthIndicator
import io.micronaut.context.BeanContext
import io.micronaut.context.BeanRegistration
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.core.util.StringUtils
import io.micronaut.management.health.aggregator.HealthAggregator
import io.micronaut.management.health.indicator.HealthIndicator
import io.micronaut.management.health.indicator.HealthResult
import io.micronaut.management.health.indicator.annotation.Readiness
import jakarta.inject.Singleton
import org.reactivestreams.Publisher

@Singleton
@Requires(classes = [HealthIndicator::class])
@Requires(
    property = RedisHealthIndicator.NAME + ".health.enabled",
    defaultValue = StringUtils.TRUE,
    notEquals = StringUtils.FALSE,
)
@Replaces(RedisHealthIndicator::class)
@Readiness
class PbkRedisHealthIndicator(
    beanContext: BeanContext,
    healthAggregator: HealthAggregator<*>,
    redisClients: Array<RedisClient>,
    redisClusterClients: Array<RedisClusterClient>,
    private val namedServerConfigurations: List<PbkNamedRedisServerConfiguration>,
    private val defaultServerConfiguration: RedisConfiguration,
) : HealthIndicator {
    private val healthIndicator =
        RedisHealthIndicator(BeanContextWithFilter(beanContext), healthAggregator, redisClients, redisClusterClients)

    override fun getResult(): Publisher<HealthResult> = healthIndicator.result

    inner class BeanContextWithFilter(
        val originalBeanContext: BeanContext,
    ) : BeanContext by originalBeanContext {
        override fun <T : Any?> getActiveBeanRegistrations(beanType: Class<T>): Collection<BeanRegistration<T>> {
            val registrations = originalBeanContext.getActiveBeanRegistrations(beanType)

            val registrationsWithoutDisabledIndicators =
                registrations.filter { registration ->
                    val namedServerConfiguration =
                        namedServerConfigurations.find { it.name == (registration.identifier.name) }
                    val namedServerEnabled = namedServerConfiguration?.healthIndicatorEnabled == true

                    val defaultServerEnabled =
                        registration.identifier.name == DEFAULT_BEAN_IDENTIFIER &&
                            defaultServerConfiguration.defaultServerHealthIndicatorEnabled

                    namedServerEnabled || defaultServerEnabled
                }

            return registrationsWithoutDisabledIndicators
        }
    }

    companion object {
        const val DEFAULT_BEAN_IDENTIFIER = "Primary"
    }
}
