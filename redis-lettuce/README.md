# Redis lettuce

Changes the behaviour of `io.micronaut.redis:micronaut-redis-lettuce` to match our guidelines.

Include this module in every project where you interact with `redis-lettuce`.

## :bangbang: Important :bangbang:

When using `micronaut-cache`:
- set property `redis.servers.<cache-server>.timeout` to `1s`.
If you don't have this set up and the Redis connection goes down, the functions annotated with `@Cacheable` will wait 60 seconds before
executing.
- disable health indicators for the cache server. If the Redis connection went down, we don't want to restart the application.

## Configuration

The [configuration properties of micronaut-redis-lettuce](https://micronaut-projects.github.io/micronaut-redis/5.4.0/guide/configurationreference.html) are still available.

- `redis.init-connection-retry-delay:5s` - How long to wait between connection retries on application startup
- `redis.default-server-enabled:true` - If the default server is enabled. When false, the app won't try to connect to it
- `redis.default-server-health-indicator-enabled:true` - If the health indicator for the default server is enabled
- `redis.servers.*.enabled:true` - If the server is enabled. When false, the app won't try to connect to it
- `redis.servers.*.health-indicator-enabled:true` - If the health indicator for the server is enabled

## Setup

```kotlin
// build.gradle.kts

dependencies {
    implementation("cz.pbktechnology.platform.common:redis-lettuce:$platformCommonVersion")
}
```

## Common problems

### App won't connect to the AWS ElastiCache
If the app can't connect to the AWS ElastiCache, check that you have the following configuration.
```yaml
redis:
  ssl: true # <--
```

## Behaviour changes

- Adds retries when connecting to Redis fails on startup
- When disconnected, functions annotated with `@Cacheable` won't fail
- Adds disabling for servers and caches by configuration

## Testing

To run tests that depend on Redis without running a Redis server, set `redis.default-server-enabled` and `redis.servers.*.enabled` to false.
If you use `micronaut-cache`, set `redis.caches.<cache-name>.enabled` to `false`.
