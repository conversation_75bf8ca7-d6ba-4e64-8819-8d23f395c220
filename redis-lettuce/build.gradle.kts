plugins { id("cz.pbktechnology.platform.common.gradle-plugin.module") }

platform { publishing { artifactId = "redis-lettuce" } }

tasks.test { useJUnitPlatform() }

dependencies {
    kapt(platform(project(":bom")))
    implementation(platform(project(":bom")))
    implementation(project(":core"))
    implementation(project(":api"))

    kapt("io.micronaut:micronaut-inject-java") // to be injectable in other projects
    implementation("io.micronaut:micronaut-inject") // to compile annotations
    implementation("io.micronaut:micronaut-management")
    implementation("io.micronaut.redis:micronaut-redis-lettuce")
    implementation("io.github.microutils:kotlin-logging-jvm")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("io.micronaut.tracing:micronaut-tracing-opentelemetry-http")

    testImplementation(project(":logging-logback"))
    testImplementation(kotlin("test-junit5"))
    testImplementation("io.micronaut.test:micronaut-test-junit5")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactive")
    testImplementation("io.mockk:mockk")
    testImplementation("org.assertj:assertj-core")
    testImplementation("io.opentelemetry:opentelemetry-sdk-testing")
    kaptTest("io.micronaut:micronaut-inject-java")
    testImplementation(platform("org.testcontainers:testcontainers-bom"))
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:toxiproxy")
    testImplementation("org.assertj:assertj-core")
}
