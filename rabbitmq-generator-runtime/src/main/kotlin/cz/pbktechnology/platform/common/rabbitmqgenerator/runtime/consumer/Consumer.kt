package cz.pbktechnology.platform.common.rabbitmqgenerator.runtime.consumer

import com.rabbitmq.client.AMQP
import com.rabbitmq.client.AlreadyClosedException
import com.rabbitmq.client.Channel
import com.rabbitmq.client.DefaultConsumer
import com.rabbitmq.client.Delivery
import com.rabbitmq.client.Envelope
import com.rabbitmq.client.RecoverableChannel
import com.rabbitmq.client.ShutdownSignalException
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.micronaut.messaging.exceptions.MessageListenerException
import io.micronaut.rabbitmq.connect.ChannelPool
import io.micronaut.rabbitmq.intercept.RabbitMQConsumerAdvice
import mu.KotlinLogging
import java.io.IOException
import java.time.Clock
import java.time.Duration
import java.time.LocalDateTime
import java.util.concurrent.ExecutorService
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * Forked from [RabbitMQConsumerAdvice.RecoverableConsumerWrapper]
 *
 * This wrapper around a [com.rabbitmq.client.DefaultConsumer] allows to handle the different signals from
 * the underlying channel and to react accordingly.
 *
 *
 * If the consumer is canceled due to an external event (like an unavailable queue) we will try to recover from it.
 * Exceptions that are caused by the consumer itself will not trigger the recovery process. In such a case the
 * consumer will no longer receive any messages.
 *
 * @see com.rabbitmq.client.Consumer.handleShutdownSignal
 * @see com.rabbitmq.client.Consumer.handleCancel
 */
class Consumer(
    private val queue: String,
    private val consumerTag: String,
    private val executorService: ExecutorService?,
    private val exclusive: Boolean,
    private val arguments: Map<String, Any?>,
    private val channelPool: ChannelPool,
    private val prefetch: Int?,
    private val deliverCallback: DeliverCallback,
    private val autoAcknowledgment: Boolean,
    val topologyId: String,
    val connectionRetryDelay: Duration,
    registerOnStartup: Boolean,
    retryRegistry: RetryRegistry,
    clock: Clock,
) {
    private var currentState: State = State.CANCELLED
        @Synchronized get

        @Synchronized set

    private var desiredState: State =
        when (registerOnStartup) {
            true -> State.REGISTERED
            false -> State.CANCELLED
        }
        @Synchronized get

        @Synchronized set
    private var desiredStateLastSetAt = LocalDateTime.now(clock)

    /** True when cancelled due to readiness probe */
    private var isTemporarilyCancelled = AtomicBoolean(true)
    private var isTemporarilyCancelledLastSetAt = LocalDateTime.now(clock)

    private val logger = KotlinLogging.logger { }

    private val handlingDeliveryCount = AtomicInteger()
    private var consumer: DefaultConsumer? = null

    private val registerRetry =
        retryRegistry.retry(
            "${this::class.qualifiedName}:register",
            RetryConfig
                .Builder<Unit>()
                .maxAttempts(Int.MAX_VALUE)
                .waitDuration(connectionRetryDelay)
                .build(),
        )
    private val cancelRetry =
        retryRegistry.retry(
            "${this::class.qualifiedName}:cancel",
            RetryConfig
                .Builder<Unit>()
                .maxAttempts(Int.MAX_VALUE)
                .waitDuration(connectionRetryDelay)
                .build(),
        )

    @Synchronized
    fun setDesiredState(
        state: State,
        timestamp: LocalDateTime,
    ) {
        if (timestamp > desiredStateLastSetAt) {
            desiredState = state
            desiredStateLastSetAt = timestamp
        }
    }

    @Synchronized
    fun setIsTemporarilyCancelled(
        isCancelled: Boolean,
        timestamp: LocalDateTime,
    ) {
        if (timestamp > isTemporarilyCancelledLastSetAt) {
            isTemporarilyCancelled.set(isCancelled)
            isTemporarilyCancelledLastSetAt = timestamp
        }
    }

    @Synchronized // To prevent registering and cancelling at the same time
    fun reconcileState() {
        val finalDesiredState =
            if (isTemporarilyCancelled.get()) {
                State.CANCELLED
            } else {
                desiredState
            }
        logger.debug {
            "Reconciling consumer [$topologyId] state. desiredState=[$desiredState], finalDesiredState=[$finalDesiredState], currentState=[$currentState]"
        }

        if (finalDesiredState == currentState) {
            return
        }

        when (finalDesiredState) {
            State.REGISTERED -> register()
            State.CANCELLED -> cancel()
        }
    }

    /**
     * Use a channel from the pool and register the consumer
     */
    private fun register() {
        logger.debug { "Registering consumer [$topologyId]" }

        registerRetry.executeRunnable {
            if (desiredState == State.CANCELLED) {
                logger.warn { "Consumer's desired state is cancelled, aborting register" }
                return@executeRunnable
            }

            var channel: Channel? = null
            try {
                channel = channelPool.channel
                consumer = createConsumer(channel)
            } catch (e: InterruptedException) {
                logger.warn(e) {
                    "The consumer [$consumerTag] recovery was interrupted. The consumer will not recover."
                }
                Thread.currentThread().interrupt()
                return@executeRunnable
            } catch (e: Exception) {
                if (channel != null) {
                    channelPool.returnChannel(channel)
                }
                logger.error(e) { "Registering consumer [$topologyId] failed, will retry in [${connectionRetryDelay.seconds}]s " }
                throw e
            }

            currentState = State.REGISTERED
            logger.info { "Registered consumer [$topologyId]" }
        }
    }

    /**
     * Cancel the consumer and return the associated channel to the pool.
     */
    private fun cancel() {
        logger.debug { "Cancelling consumer [$topologyId]" }

        val channel = checkNotNull(consumer).channel

        cancelRetry.executeCallable {
            try {
                channel.basicCancel(consumerTag)
            } catch (e: IOException) {
                // ignore
            } catch (e: AlreadyClosedException) {
                // ignore
            } catch (e: Exception) {
                logger.error(e) { "Cancelling consumer [$topologyId] failed, will retry in [${connectionRetryDelay.seconds}]s " }

                throw e
            }
        }

        try {
            while (handlingDeliveryCount.get() > 0) {
                Thread.sleep(500)
            }
        } catch (e: InterruptedException) {
            Thread.currentThread().interrupt()
        } finally {
            consumer = null
            channelPool.returnChannel(channel)
            currentState = State.CANCELLED
        }
        logger.debug { "Cancelled consumer [$topologyId]" }
    }

    private fun createConsumer(channel: Channel?): DefaultConsumer {
        setChannelPrefetch(prefetch, channel)

        val consumer: DefaultConsumer =
            object : DefaultConsumer(channel) {
                /**
                 * The consumer was irregular terminated. This may be caused by a deleted or temporary unavailable
                 * queue.
                 *
                 *
                 * This kind of infrastructure failure may happen due to RabbitMQ cluster node restarts or other
                 * external actions. The client application will most likely be unable to restore the infrastructure,
                 * but it should return to normal operation as soon as the external infrastructure problem is solved.
                 * e.g. the RabbitMQ node restart is complete and the queue is available again.
                 */
                override fun handleCancel(consumerTag: String) {
                    synchronized(this@Consumer) {
                        <EMAIL> = null
                        channelPool.returnChannel(getChannel())
                    }

                    if (channelPool.isTopologyRecoveryEnabled && getChannel() is RecoverableChannel) {
                        logger.warn {
                            "The consumer [$consumerTag] subscription was canceled, a recovery will be tried."
                        }

                        currentState = State.CANCELLED
                        reconcileState()
                    } else {
                        error("Only recoverable channels are supported")
                    }
                }

                /**
                 * A shutdown signal from the channel or the underlying connection does not always imply that the
                 * consumer is no longer usable. If the automatic topology recovery is active and the shutdown
                 * was not initiated by the application it will be recovered by the RabbitMQ client.
                 *
                 *
                 * If the topology recovery is enabled we will also try to recover the consumer if (only) its channel
                 * fails. These events are not handled by the RabbitMQ client itself as they are most likely application
                 * specific. Also some edge cases like a delivery acknowledgement timeout may cause a channel shutdown.
                 * The registered exception handler of the consumer may handle these cases and it is possible to
                 * resume message handling by re-registering the consumer on a new channel.
                 */
                override fun handleShutdownSignal(
                    consumerTag: String,
                    sig: ShutdownSignalException,
                ) {
                    if (getChannel() is RecoverableChannel && sig.isHardError) {
                        // The RabbitMQ client automatic recovery is only triggered by connection errors.
                        // The consumer will be recovered by the client, so no additional handling here.
                        logger.error {
                            "The underlying connection was terminated. Automatic recovery attempt is underway for consumer [$consumerTag]"
                        }
                    } else if (channelPool.isTopologyRecoveryEnabled && getChannel() is RecoverableChannel) {
                        logger.error(sig) {
                            "The channel of this consumer was terminated. Automatic recovery attempt is underway for consumer [$consumerTag]"
                        }
                        synchronized(this@Consumer) {
                            <EMAIL> = null
                            channelPool.returnChannel(getChannel())
                        }
                        currentState = State.CANCELLED
                        reconcileState()
                    } else {
                        error("Only recoverable channels are supported")
                    }
                }

                override fun handleDelivery(
                    consumerTag: String,
                    envelope: Envelope,
                    properties: AMQP.BasicProperties,
                    body: ByteArray,
                ) {
                    // If the broker forces the channel to close, the client may already have prefetched messages in
                    // memory and will call handleDelivery for these messages, even if they are re-queued by the broker.
                    // The client will be unable to acknowledge these messages. So it is safe to silently discard
                    // them, without bothering the callback handler.
                    // In addition, consuming of queued messages is stopped when the consumer is canceled.
                    if (currentState == State.CANCELLED || !getChannel().isOpen) {
                        return
                    }
                    handlingDeliveryCount.incrementAndGet()
                    if (executorService != null) {
                        executorService.submit(Runnable { callbackHandle(envelope, properties, body) })
                    } else {
                        callbackHandle(envelope, properties, body)
                    }
                }

                fun callbackHandle(
                    envelope: Envelope?,
                    properties: AMQP.BasicProperties?,
                    body: ByteArray?,
                ) {
                    try {
                        deliverCallback(getChannel(), Delivery(envelope, properties, body))
                    } finally {
                        handlingDeliveryCount.decrementAndGet()
                    }
                }
            }

        channel!!.basicConsume(queue, autoAcknowledgment, consumerTag, false, exclusive, arguments, consumer)
        return consumer
    }

    enum class State {
        REGISTERED,
        CANCELLED,
    }

    companion object {
        private fun setChannelPrefetch(
            prefetch: Int?,
            channel: Channel?,
        ) {
            try {
                if (prefetch != null) {
                    channel!!.basicQos(prefetch)
                }
            } catch (e: IOException) {
                throw MessageListenerException(
                    String.format(
                        "Failed to set a prefetch count of [%s] on the channel",
                        prefetch,
                    ),
                    e,
                )
            }
        }
    }
}
