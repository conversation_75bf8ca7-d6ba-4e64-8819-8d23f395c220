# 2.2.2

- Fix correlation id in logs of server errors that happen before `BaseResponseHelper`
- Make `redis-lettuce` health indicator toggleable by configuration
- Add configuration validation for Redis cache timeout

# 2.2.1

Fix `NullPointerException` errors that were causing "Got unhandled exception: null" logs and 500 responses

# 2.2.0

> :warning: `NullPointerException` might occur in http server. Upgrade to 2.2.1 if your service acts as http server.

Add `JaCoCo` to Platform Gradle plugin 

# 2.1.3

> :warning: `NullPointerException` might occur in http server. Upgrade to 2.2.1 if your service acts as http server.

Make registering/unregistering of consumers in `rabbitmq-generator` more stable when application readiness changes quickly

# 2.1.2

> :warning: `NullPointerException` might occur in http server. Upgrade to 2.2.1 if your service acts as http server.

Fix `NullPointerException` errors that were causing "Got unhandled exception: null" logs and 500 responses in approximately 0.02% of requests

# 2.1.1

> :warning: `NullPointerException` might occur in http server. Upgrade to 2.2.1 if your service acts as http server.

Fix detection of application health. When no liveness indicator was defined, service health was not set to UP.

# 2.1.0

> :warning: `NullPointerException` might occur in http server. Upgrade to 2.2.1 if your service acts as http server.

> :warning: K8S probes do not work correctly. Upgrade to 2.1.1.

Improve `rabbitmq-generator`:
 - add probes integration
 - add published time to message
 - add topology verification at startup - service won't start if RabbitMQ is missing required elements
 - fix interoperability with old client and configuration `rabbitmq.check-queue`

# 2.0.0

> :warning: `NullPointerException` might occur in http server. Upgrade to 2.2.1 if your service acts as http server.

> :warning: K8S probes do not work correctly. Upgrade to 2.1.1.

## Changes

- Upgrade kotlin to `1.9.25`
- Change configuration of user context propagation
- Add new ktlint rule that checks usage of `@Transactional` on top of suspendable functions
- Fix `RequestsProcessingMetricExporter` to start only when Micrometer is installed
- False positive logging. For details see [README.md](api/README.md#error-handling)
- Make correlation id propagation implicit - the correlation id no longer needs to be specified in the OAS
- Create `core` module, with service health logic and `Clock` bean for time travel
  - [Documentation](core/README.md)
- Create `core-test` module with utilities to mock `Clock`
- `TimeZoneConfiguration` in `api` module deprecated in favor of one in `core` module
- Rabbitmq consumers get disconnected when service is unhealthy
- Add retries on service startup for:
  - db
  - rabbitmq
- Add minio health indicator
- Remove deprecated
  - utils
    - `AnyUtil`
  - security
    - `UserContext.getAdvisorId`
    - `UserContext.getUserId`
    - `PbkSecurityService.getPbkAuthentication`
    - `PbkSecurityService.getUserId`
  - rabbitmq
    - `BaseProducer.init`
    - `BaseConsumer.registerConsumer(consumer: Consumer<T>)`
  - minio
    - `CustomLoggingInterceptor.getLevel`
    - `CustomLoggingInterceptor.setLevel`
  - logging
    - `SensitivityLevel.MODERATE`
- Remove
  - test-utils
    - `EitherTestUtil` - replace it with extension function
    - `GeneralTestExtension`
    - `GeneralTestUtil`
  - utils
    - everything
- Rename
  - security
    - `UserContext.getAdvisorIdAsInt` to `UserContext.getAdvisorId`
    - `UserContext.getUserIdAsInt` to `UserContext.getUserId`
    - `PbkSecurityService.getPbkAuthenticationNullable` to `PbkSecurityService.getPbkAuthentication`
    - `PbkSecurityService.getUserIdAsInt` to `PbkSecurityService.getUserId`

## :bangbang: Migration :bangbang:

### Upgrade kotlin version

If you are not on our gradle plugin, upgrade the kotlin version manually to `1.9.25`

### User Context propagation

User context propagation is now controlled by `user-context-propagation` property on http service, which **is disabled by default**. 
Here is example of configuration for enabling user context propagation:

```yaml
micronaut:
  http:
    services:
      cz-partners-testapi-v1-client:
        url: ...
        user-context-propagation: true
```

The following configurations are legacy, **remove them**: `micronaut.http.client.user-context-propagation-regex` and `micronaut.http.client.user-context-propagation-ant`

### Utils/test-utils module replacement

Ideally rewrite the functions with kotlin/junit built-ins. Or you can copy the functions to your codebase.

### Codegen upgrade

For error handling to work correctly, codegen version `0.19.0` is required

### Probes

- Upgrade UHC to `2.0.0` or newer to get new k8s probes settings
- Set configuration property `endpoints.health.details-visible` to `anonymous`. This provides more information to k8s when probing the application and makes debugging easier.
- If the app is exposed via ingress, forbid access to `/health/liveness` and `/health/readiness` endpoints
- Add `core` module to `build.gradle.kts` files for all services (no need to add it to `db-init` etc.) to enable customized health endpoints.
- Update non-standardized (by platform-common) inputs to receive work only when service is ready.

  **Why**: When service is not ready, there's a high probability that the work that it's doing will fail.
  For that reason, it doesn't make sense to receive more work, it's better to redirect this new work to other replicas (that might be ready).

  **Edge cases**: It's possible that in a given moment no replicas will be ready, and in that case the work request can be lost (e.g. in scheduled jobs).
  In most cases, this is ok, but sometimes you can't afford to lose the work request.
  **Think it through thoroughly, case by case**. If you are unsure, contact the platform team.

  **Exceptions**: Right now we chose to ignore `quartz`. We will take care of it later after changing the whole concept of jobs.

  **How**: You have two options. Either use `WhenApplicationReady` annotation (from `core` module) or check the readiness manually from `ApplicationHealth`
  ```kotlin
  open class Example(private val applicationHealth: ApplicationHealth){
    @Scheduled
    @SchedulerLock // Works with ShadLock
    @WhenApplicationReady
    open fun myJobThatExecutesOnlyWhenAppReady() {} 
  
    fun processOtherWorkOnlyWhenAppReady(){
      if(!applicationHealth.isReady)
        return
    }
  }
  ```
- Change imports of `TimeZoneConfiguration` to `cz.pbktechnology.platform.common.core.time.TimeZoneConfiguration` module

> :bulb: More detail about probes as a whole can be found at [Confluence](https://projektpb.atlassian.net/wiki/x/dQDa2g)

# 1.13.0

Backport of [2.1.0](#210) without probes integration

# 1.12.3

Publish `rabbitmq-generator-gradle-plugin` module. This time for real.

# 1.12.2

Publish `rabbitmq-generator-gradle-plugin` module

# 1.12.1

Add option to have one channel per RabbitMQ producer. Enabled by default. To disable it, see [README](./rabbitmq/README.md)

# 1.12.0

Add `excludeLog4j` and `excludeSlf4jNop` configuration to module gradle plugin. [Documentation](./gradle-plugin/README.md)

# 1.11.6

Add check if all RabbitMQ queues and exchanges, that the service is using, are created. Enabled by default. To disable
it, see [README](./rabbitmq/README.md)

# 1.11.5

Add minio delete method without object version

# 1.11.4

Fix startup of services with Quartz

# 1.11.3

Remove logging of request body and headers on `ConversionErrorException`

# 1.11.2

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Up Netty to 4.1.118.Final because CVE-2025-24970 

# 1.11.1

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Fix `@Cacheable` for suspended functions

# 1.11.0

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Add disabling for Redis servers and caches by configuration

# 1.10.1

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`


Fix validation of `gateway.yaml`

# 1.10.0

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`


Add options to use streams for downloading/saving files via S3 client to reduce memory usage

# 1.9.0

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

- Make functions annotated with `@Cacheable` work, when disconnected from Redis 
- Add validation of `gateway.yaml` to gradle plugin. It validates that you use only specs from `servers.conf` and that each spec appears only once.

# 1.8.5

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Bump Quartz version to 2.5.0 in bom

# 1.8.4

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Fix ktlint to ignore generated sources on Windows 

# 1.8.3

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Remove `AntiDeadlockTransactionalInterceptor`

# 1.8.2

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Make `BaseResponseHelper.getErrorResult` public

# 1.8.1

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Add request processing abort when the request is cancelled.
More info in [README](./api/README.md).

# 1.8.0

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

add ExceptionLoggingHandler for Rabbit to provide capability to override default exception logging behaviour.

# 1.7.0

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Add metric for requests processing count (`platform_http_requests_processing_count`)

# 1.6.0

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Improve configuration validation, most of the validations will now check only main `application.yml`.

# 1.5.3

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Fix ktlint disabling

# 1.5.2

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: `@Cacheable` annotation doesn't work properly. Upgrade to 1.11.1 if you want to use it.

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

- Fix context propagation on suspended functions annotated with `@Cacheable`.
- Fix retry on startup when using Redis caches.

# 1.5.1

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Fix `KvStorage` bean when using unnamed redis server.

# 1.5.0

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Deprecate `SensitivityLevel.MODERATE`. Replace it with `SensitivityLevel.HIGH` or `SensitivityLevel.LOW`.

# 1.4.0

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Make pre commit hooks(installed by gradle-plugin) configurable. For details see [README](./gradle-plugin/README.md#pre-commit-hooks).

# 1.3.4

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Fix `generateInfoEndpointData` in gradle plugin

# 1.3.3

> :warning: Use only with `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration set to `false`

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Set `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration to `true` by default. 
This fixes deadlocks when using transactional annotation.

# 1.3.1

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Fix transactions in micronaut tests when `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration enabled.

# 1.3.0

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

## Changes

Add `redis-lettuce` module that changes behaviour of the `redis-lettuce` library.

## :bangbang: Migration :bangbang:

If you use the `redis-lettuce` library add this new module to your dependencies.
More detail in [README](redis-lettuce/README.md)

# 1.2.3

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

- Fix deadlocks when using transactional annotation. 
It's still experimental and thus disabled by default. 
You can enable it using `feature-toggles.persistence.enable-anti-deadlock-transactions` configuration.
- Add support for Redis named servers in kv-storage

# 1.2.2

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Add configuration validation and log4 excludes to gradle plugin

# 1.2.1

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Add new functions for working with ids in `UserContext`
  - `UserContext.getUserIdAsInt`
  - `UserContext.getAdvisorIdAsInt`
  - `PbkSecurityService.getUserIdAsInt`

Deprecated old functions for working with ids in `UserContext`
  - `UserContext.getAdvisorId`
  - `UserContext.getUserId`
  - `PbkSecurityService.getUserId`

# 1.2.0 Redis, Gradle plugin, Logging restructuring

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

## Redis
- Add `kv-storage` and `kv-storage-redis-lettuce` modules. More details in readmes [here](kv-storage/README.md) and [here](kv-storage-redis-lettuce/README.md).
- Add documentation around caching [here](./techStackDocs.md#caching).


## Gradle plugin
The plugin is still experimental, for details see the [readme](gradle-plugin/README.md).

## Logging restructuring
- Removed `kotlin-logging` module that nobody (hopefully :pray:) uses
- Moved `SensitivityLevel` to `logging` module

# 1.1.3 Fix User Context (UC) propagation

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

User Context propagation has been altered in this Merge request

- Fix of propagation from rest controller to rest client introduced by 1.1.0
- Fix of HTTP client propagation (unreliable propagation) due to HTTP filter exectuion
- Service user UC propagation in every case (before, service user UC was not propagated)
- Code First clients require change or rewrite to Design First


> This version requires usage of codegen version 0.16.3 for correct function.

> For information for debugging UC propagation and code first clients, check details in [Propagation of X-User-Context](security/README.md#propagation-of-x-user-context)

# 1.1.2 Change identification of service user

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Due to upgrade to Keycloak 25 which removed claim `session_state` from token and which was used to identify service user,
now `user_attributes` is used instead.

# 1.1.1 Fix request processing thread pool

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

All request processing now happens outside netty thread pool. That leads to increased throughput.

# 1.1.0 Exchange OpenTracing for OpenTelemetry and integration with Dynatrace

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

## Notable changes:

### Removal of OpenTracing (B3) and introduction of OpenTelemetry

Deprecates Zipkin configuration. Please remove it from your application.yaml

```yaml
tracing:
  zipkin:
    http:
      url: ${ZIPKIN_URL:`http://localhost:9411`}
    enabled: true
    sampler:
      probability: 0
```

### Removal of trace-id from logs

TraceId is removed from logging, as it doesn't serve useful purpose.

### HttpClient scheduler change

HTTP client now executes in the parent coroutine's scheduler instead of always IO. This prevents unnecessary thread switching.

### Propagation of X-User-Context header through http client

the http client now propagates X-User-Context indiscriminately of the counterpart api. That means if the api doesn't have X-User-Context header, it will still get propagated. This happens on all operations. There are two new variables to control this behavior:

`micronaut.http.client.user-context-propagation-regex` - you can configure which micronaut services propagate the context. By default, only internal clients do this. You can change the regex to select the services you want to exclude/include. Micronaut services has to be used.

default value: `cz-partners-.*`

`micronaut.http.client.user-context-propagation-ant` - You can further limit the endpoints that this applis to by specifying the ant matcher.

default value: `/**`

    Pay attention to this! Incorrect setup may cause User Context leaking. Contact PTS team for CR if this mechanism is not sufficient.

### Propagation of X-User-Context header through RabbitMQ

X-User-Context now propagates through RabbitMq as well. You can use it in consumers as usual through `PbkSecurityService`

### Refactor of ContextConfiguration

ContextConfiguration methods have been refactored to correctly setup contexts for tracing.

We expose the following methods:

- `ContextConfiguration.initialContext`: FOR USE WITH TESTS ONLY, NOT PRODUCTION CODE

- `ContextConfiguration.correlationContext`: INTENDED FOR PLATFORM CODE USE ONLY!

All other methods are removed, as they were duplicates of the functionality without real reason to exist.

### Introduction of ContextProvider

New injectable bean to execute workloads is introduced:

- `contextProvider.withConsumerContext`: INTENDED FOR PLATFORM CODE USE ONLY! Used in MQ Consumer.
-
- `contextProvider.withJobContext`: Intended for use in Scheduled Jobs and Test contexts. Wrap the work in this context to enable tracing.

```kotlin
contextProvider.withJobContext("Data Warehouse Load Scheduled Job") {
                    //Do Work
                }
```

    Reach out to PTS team for advice on migration if unsure or you have some uncovered case.

### Change of MDC propagation: Thread housekeeping

MDC is now managed in a way that threads that finish coroutine are not polluted by that coroutine's MDC.

### ErrorHandlers in context

Thanks to the previous point, Error handlers run out of context when handling thrown exceptions, so we have to have a way to communicate the context to them. Thanks to them running in HTTP Request's lifecycle, they use ServerRequestContext to propagate the context information. This enables them to log correctly.

A new superclass is introduced, which all handlers should extend: `ContextAwareHandler`

Please migrate any custom ExceptionHandlers to subclass this.

### Disabling of otel in tests

Thanks to the way that OTEL is designed, it doesn't play nicely with some @MicronautTests. The OTEL instrumentation should be disabled in tests with the following configuration.

```yaml
micronaut:
  otel:
    enabled: false
```

# 1.0.1

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

Updated migration recipe `cz.pbktechnology.platform.common.migration-recipes.1_0_0` to add uhc version to info endpoint

# 1.0.0

> :warning: On `ConversionErrorException` (occurs e.g. when server and client api specs do not match ) headers and
> request body are logged. To mitigate this issue, upgrade to `1.11.3`

## Changes
- Created new package `persistence` that replaces `bank-jpa` and `pid-jpa`
- Removed deprecated `rabbitmq` module
- Removed `security-api` module
- Removed `oauth2` module
- Renamed `rabbitmq2` to `rabbitmq`
- Renamed all packages to `cz.pbktechnology.platform.common.xxx`
- Removed `cz.partners.common.model` package - available in `cz.partners.pid.common.model` (pid-common)
- Removed phone helpers in `security` module - use `pid-api` module from `pid-common`
- Remove some deprecated stuff
  - `cz.pbktechnology.platform.common.api.ServiceUser`
  - `cz.pbktechnology.platform.common.api.UnauthorizedUser`
  - `cz.pbktechnology.platform.common.security.api.PidAuthentication`
  - `cz.pbktechnology.platform.common.security.api.ServiceRequest`
  - `cz.pbktechnology.platform.common.api.RequestedOperationSignData`
  - `cz.pbktechnology.platform.common.api.Empty`
  - `cz.pbktechnology.platform.common.util.EitherUtil.toEmpty()`
  - `cz.pbktechnology.platform.common.controller.SystemController`
  - `cz.pbktechnology.platform.common.logger.PbkLoggingConfigurationFactory`
  - `:api Success and Error thymeleaf templates`
  - `:api validation.properties business messages (iban, positiveAmountIsoCurrency)`
  - `cz.pbktechnology.platform.common.security.api.Authentication`
  - `cz.pbktechnology.platform.common.security.api.PbkServiceUser`
- Renamed `pidObjectMapper` function in `api` module to `platformObjectMapper`
- Moved `hazelcast` module here
- Remove periodic restarts

> :warning:  Make sure to migrate dependencies first. When switching to platform-common all dependencies must be switched too.


## Automated migration
Use OpenRewrite recipe `cz.pbktechnology.platform.common.migration-recipes.1_0_0` to automate the migration process

```bash
./gradlew rewriteRun --init-script <pathToMigrationGradle>  -Drewrite.activeRecipe=cz.pbktechnology.platform.common.migration-recipes.1_0_0
```

[**How to run a recipe**](migration-recipes/README.md#how-to-run-a-recipe)


### After the automatic migration:
- check generated todos and resolve them. Todos have format `TODO(migration)`
- if needed add `pid-api` module from `pid-common`
- you might need to fix imports
  - of some models (`cz.partners.pid.common.model` -> `cz.pbktechnology.platform.common.model`
  - of phone helpers from `security` module to `pid-api` module from `pid-common`
  - of some exceptions to `pid-api` module from `pid-common`
- check that `commonVersion` in the info endpoint was changed to `pidCommonVersion`
- check that `platformCommonVersion` was added to the info endpoint
- check that `uhcVersion` was added to the info endpoint (`UHC_VER` env variable)
- check that `micronaut.health.recycle` settings were removed
- recommended, not necessary
  - upgrade UHC version to `>= 1.48.0` so that the info endpoint has access to the `UHC_VER` env variable

***
# Changelog from pid-common

# 12.5.0

Added support for logging in JSON format. The JSON format is not enabled by default. For more info see [logging-logback](logging-logback/README.md) documentation. Jira issue: [PTS-591](https://projektpb.atlassian.net/browse/PTS-591)

changes in configuration:

```yaml
logger:
  # this alias becomes mandatory in config so we can switch the loki off in the future
  loki-enabled: ${LOKI_ENABLED:false}
  ...
  # this alias is mandatory in configuration so we can enable json logging in environments when the env is ready
  console-json:
    enabled: ${CONSOLE_JSON_ENABLED:false}
```

# 12.4.0

Fix token caching problem caused by `12.1.0`

# 12.1.0

Micronaut fix https://github.com/micronaut-projects/micronaut-security/pull/1379
This is a hotfix of earlier patch which was not effective
When oauth client tries to retrieve token and Keycloak fails or is inaccessible, the retrieval fails and service gets into inconsistent state. The fail is cached and result in subsequent instant read timeouts.
This fix applies the upstream patch correctly, resulting in recovery of the failed state.

This patch negates the need for `# 10.1.0 Automatic service restart capability`

# 11.0.0 Removed "old-security" and "new-security" Micronaout environments

Removed support to use old and new security approaches simultaneously (pid-common's libraries `security` and `security-api` (resp. `oauth2`))
by removal of support for "old-security" and "new-security" Micronaout environments.

# 10.1.0 Automatic service restart capability

Added new `PeriodicRestartHealthIndicator` Health Indicator.

This health indicator is responsible for setting health indicator DOWN after certain amount of time. The time is randomized between `lowerbound` and `upperbound` settings. This indicator is disabled by default.

When the health indicator goes down, the pod gets restarted by k8s. Please pay attention to the following requirements before enabling this feature:

- Service must have rollout strategy
- Service must run in at least two instances (HA)
- Service should not have long running jobs that aren't idempotent. K8s will issue SIGTERM signal to service and Micronaut terminates, cancelling the running jobs.

Configuration example:
```yaml
mandatory:
micronaut.health.recycle.enabled: true #(default false)
optional:
micronaut.health.recycle.lowerbound: 1d #(default 1d)
micronaut.health.recycle.upperbound: 2d #(default 2d)
```

# 9.1.1 RabbitMQ Test library fix

If the `rabbitmq-test` library from `pid-common` is utilized, a mock RabbitMQ connection with a reconnectable 
interface is established. If your project relies on this test library, please utilize this version instead of `9.1.0`.

# 9.1.0 RabbitMQ reconnect health check

When RabbitMQ client reconnects after connection loss, health check probe is set UP.

# 8.8.0 Revert update kotlin

Problems with service building forces revert of kotlin 1.9.22 -> 1.7.20
- Tags 8.6.1 to 8.7.0 should not be consumed (kotlin version and Micronaut incompatibility)

# 8.7.0 Extend client exceptions

Adding a subtype ExternalPbkNotFoundException in ExternalPBkException for easier exception handling.
Adding an exception when the counterparty returns an empty body with codes 204, 201.
Returning the status code in the exception

# 8.6.2

Update Minio
- mitigate org.apache.commons:commons-compress: CVE-2024-25710

Update Kotlin to 1.9.22
 - Consumers need to migrate to kotlin version in consuming services

# 8.6.1

Update Postgres
- mitigate postgresql: CVE-2024-1597

# 8.2.0

Fix Micronaut client Oauth code to retry Identity provider (IDP) connection when IDP goes down and back up.

Enhance `logging-logback` 
- allow configure log batch sending properties
- allow configure log batch sending retries

# 8.0.1

Update Logback to 1.2.13

# 6.3.0

Backport `rabbitmq2` and `rabbitmq` with monitoring closed channel from tag `5.9.0`

# 6.0.1

### Add new UserContext helpers
- getAdvisorId - returns advisorId from jwt userAttributes
- getPfsUsername - returns pfsUsername

# 6.0.0

# 5.9.0

The `rabbitmq` will use wrapper to intercept channel close and propagate 
connection shutdown to health indicator. The feature is by default disabled 
and can be enabled by setting `rabbitmq.wrap-channel.toggle` to `true`. 
Close channel will lead to application restart.

# 5.8.0

release of `rabbitmq2` library that supersedes `rabbitmq` library

[README.md](rabbitmq2%2FREADME.md)
    
# 5.5.0

### Update bom dependencies

bouncyCastle jdk15on: 1.70
minio: 8.5.6
pebble: 3.2.1 (zmena importu)
amqpClient: 5.18.0

### Odchylka od micronaut-bom

jackson-bom: 2.15.3
netty-bom: 4.1.100.Final
snappy-java: 1.1.10.5

### rabbitmq
odstraneni log4j zavislosti

### security
revert GHSA-qw22-8w9r-864h
pridani zavislosti: micronaut-security-oauth2

### template
prepis importu v nove verzi Pebble

### micronaut
povyseni na 3.10.1

# 5.3.4

This problem is affecting common 5.3.0 +
If you're using producer/consumer from library of other service, you will hit this problem on Producer:

```kotlin
fun init(): Unit = throw IllegalStateException("""
        Tato metoda se vola proto, ze se pouziva stary pristup: producer/consumer importovany z knihovny jine sluzby.
        V knihovne je zkompilovane AOP @PostConstruct anotace, ktera vola metodu init() bez argumentu. Nyni je v knihovne
        metoda s argumentem. Prosim prekompilujte si knihovnu s novym pristupem, aby jste je takto mohli pouzivat. Toto je
        docasne reseni ktere casem zmizi, jakmile zacneme MQ generovat a opustime knihovnovy pristup.
    """.trimMargin())
```

Exception will now be thrown in runtime. Please follow the instructions on how to get around it.

# 5.3.0

Change to RabbitMQ queue initialization

- The Producers/Consumers now initialize on ServerStartupEvent instead on PostConstruct.
- Implementers should migrate to BaseProducer<T> and BaseConsumer<T>
- Implementers should change your BaseConsumer initialization from PostConstruct to ServerStartupEvent, as described in javadoc example
- Implementers can disable queue creation by the library by setting `rabbitmq.declare-queues: false` in application configuration

Change to default ObjectMapper bean

- You can remove the `com.fasterxml.jackson.module:jackson-module-kotlin` dependency if you're using `api` module

# 2.1.4

- [ ] In mockito-core was replaced `verifyZeroInteractions` by `verifyNoInteractions`. Library `com.nhaarman.mockitokotlin2:mockito-kotlin` is not updated anymore, use `org.mockito.kotlin:mockito-kotlin`. See [issue](https://github.com/mockito/mockito-kotlin/issues/383)
```diff
- testImplementation("com.nhaarman.mockitokotlin2:mockito-kotlin")
+ testImplementation("org.mockito.kotlin:mockito-kotlin")
```

# 2.0.0

### Main changes:

- Micronaut 3.3.4 => 3.8.5
- Kotlin 1.6.10 => 1.7.20

### Service Migration Checklist

- [ ] **Remove** following section from your `build.gradle.kts` (if present)

```diff
-    tasks.withType<io.micronaut.gradle.graalvm.NativeImageTask> {
-        args(
-            "--verbose",
-            "-H:+TraceClassInitialization",
-            "--report-unsupported-elements-at-runtime"
-        )
-    }
```

- [ ] Upgrade micronaut gradle plugin version in your `build.gradle.kts` to 3.7.2+ (legacy version may vary)
```diff
     dependencies {
-        classpath("io.micronaut.gradle:micronaut-gradle-plugin:1.0.3")
+        classpath("io.micronaut.gradle:micronaut-gradle-plugin:3.7.2")
     }
 }
```

- [ ] Upgrade kotlin and micronaut versions in your `gradle.properties`
```diff
- kotlinVersion=1.6.10
- micronautVersion=3.3.4
- commonVersion=1.14.1
+ kotlinVersion=1.7.20
+ micronautVersion=3.8.5
+ commonVersion=2.0.6
```

- [ ] Exclude `slf4j-nop` dependency (with newer micronaut it sometimes gets included with transitive dependencies and breaks logging) in your main `build.gradle.kts`
```diff
subprojects {
...
    configurations.all {
        ...
+        exclude(group = "org.slf4j", module = "slf4j-nop")
    }
}

```
- [ ] Remove `schema-generate` from datasource configuration in `application.yml` if present
```diff
datasources:
  default:
    dialect: POSTGRES
    driverClassName: org.postgresql.Driver
-   schema-generate: CREATE_DROP
```

- [ ] If you encounter `Message: class org.slf4j.helpers.NOPLoggerFactory cannot be cast to class ch.qos.logback.classic.LoggerContext (org.slf4j.helpers.NOPLoggerFactory and ch.qos.logback.classic.LoggerContext are in unnamed module of loader 'app')` in micronaut tests, it can be solved by disabling metrics in `application-test.yml`
```diff

micronaut:
...
+  metrics:
+    enabled: false
```

- [ ] In `db-init` there is a possibility you encounter problems with `beforeColumn/afterColomn` in migrations. Solution is to use common at least `2.0.6` and include pid-jpa dependency in your `db-init/gradle.properties`
```diff

  dependencies {
    kapt(platform("cz.partners.common:bom:$commonVersion"))
    implementation(platform("cz.partners.common:bom:$commonVersion"))

+    implementation("cz.partners.common:pbk-liquibase:$commonVersion")

    ...
  }
```

- [ ] Dockerfile file locations may have been changed if you've had ancient micronaut plugin it is possible, that  you will need to update paths to artifacts in your `Dockerfile` [example](https://gitlab.pbk-lab.tech/branka/be/pid/pid-bpm/-/merge_requests/73/diffs)
```diff
- FROM harbor.oci.pbk-lab.tech/pid-baseimage/pid-baseimage:0.0.11
+ FROM harbor.oci.pbk-lab.tech/pid-baseimage/pid-baseimage:0.0.12
...

- COPY db-init/build/layers/libs /home/<USER>/libs
- COPY service/build/layers/libs /home/<USER>/libs
+ COPY db-init/build/docker/main/layers/libs /home/<USER>/libs
+ COPY service/build/docker/main/layers/libs /home/<USER>/libs

- COPY db-init/build/layers/resources /home/<USER>/resources
- COPY service/build/layers/resources /home/<USER>/resources
+ COPY db-init/build/docker/main/layers/resources /home/<USER>/resources
+ COPY service/build/docker/main/layers/resources /home/<USER>/resources

- COPY db-init/build/layers/application.jar /home/<USER>/application.jar
- COPY service/build/layers/application.jar /home/<USER>/application.jar
+ COPY db-init/build/docker/main/layers/application.jar /home/<USER>/application.jar
+ COPY service/build/docker/main/layers/application.jar /home/<USER>/application.jar
```

- [ ] In mockito-core was replaced `verifyZeroInteractions` by `verifyNoInteractions`. You need to upgrade to 2.1.x, in 2.0.x it is not fixable

******
# 1.x

unfortunately we did not manage changelog

- [ ] Remove explicit tracing dependencies (not needed with recent common-api anymore) from `service/build.gradle.kts`

```
-    //ZIPKIN
-    implementation("io.micronaut:micronaut-tracing")
-    implementation("io.opentracing.brave:brave-opentracing")
-    runtimeOnly("io.zipkin.reporter2:zipkin-reporter")
-    runtimeOnly("io.zipkin.brave:brave-instrumentation-http:5.13.2")
```

- [ ] remove custom tracing configuration

```
-    @Factory
-    class CustomTracing {
-    
-    
-        @Bean
-        fun propagationFactory(): Propagation.Factory {
-            val baggedField = BaggageField.create(X_CORRELATION_ID)
-    
-            return BaggagePropagation.newFactoryBuilder(B3Propagation.FACTORY)
-                .add(BaggagePropagationConfig.SingleBaggageField.remote(baggedField))
-                .build()
-        }
-    }
```

- [ ] prechod logovani na logback https://gitlab.pbk-lab.tech/branka/be/pid-common/-/blob/master/logging-logback/README.md


- [ ] pokud neni, povysit gradle-wrapper.jar resp gradle na verzi 7.5.1 (se starsi verzi nastava chyba 
