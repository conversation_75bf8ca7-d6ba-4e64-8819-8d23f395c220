plugins { id("cz.pbktechnology.platform.common.gradle-plugin.module") }

platform { publishing { artifactId = "security" } }

dependencies {
    implementation(platform(project(":bom")))
    kapt(platform(project(":bom")))
    implementation("io.micronaut:micronaut-runtime")
    implementation(project(":api"))
    implementation(project(":logging"))

    kapt("io.micronaut:micronaut-inject-java")
    implementation("io.micronaut:micronaut-inject")

    implementation("io.micronaut.security:micronaut-security-jwt")
    implementation("io.micronaut.security:micronaut-security-oauth2")

    implementation("io.arrow-kt:arrow-core")

    implementation("io.github.microutils:kotlin-logging-jvm")
    implementation("io.projectreactor:reactor-core:3.4.14")

    kaptTest("io.micronaut:micronaut-inject-java")
    testImplementation("io.micronaut.test:micronaut-test-junit5")
    testImplementation(kotlin("test-junit5"))
    testImplementation(project(":logging-logback"))
    testImplementation("org.assertj:assertj-core:3.24.2")
    testImplementation("io.micronaut:micronaut-http-server-netty")
    testImplementation("org.junit.jupiter:junit-jupiter-api:5.8.1")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:5.8.1")
    testImplementation("io.mockk:mockk")
    testImplementation(project(":test-utils"))
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactive")
    testImplementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    testImplementation("io.micronaut.reactor:micronaut-reactor-http-client")
}

tasks.test { useJUnitPlatform() }
