plugins { id("cz.pbktechnology.platform.common.gradle-plugin.module") }

platform { publishing { artifactId = "persistence" } }

dependencies {
    implementation(platform(project(":bom")))
    kapt(platform(project(":bom")))
    implementation(project(":core"))

    kapt("io.micronaut:micronaut-inject-java") // To be injectable in another projects
    implementation("io.micronaut:micronaut-inject") // To compile annotations

    implementation("javax.persistence:javax.persistence-api")

    implementation("io.github.microutils:kotlin-logging-jvm")

    compileOnly("io.micronaut.sql:micronaut-jdbc-hikari")
    compileOnly("io.micronaut.micrometer:micronaut-micrometer-core") // Without this `DatasourceFactory` won't be created, IDK why
}
