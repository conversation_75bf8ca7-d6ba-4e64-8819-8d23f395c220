package cz.pbktechnology.platform.common.persistence

import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.micronaut.configuration.jdbc.hikari.DatasourceConfiguration
import io.micronaut.configuration.jdbc.hikari.DatasourceFactory
import io.micronaut.context.ApplicationContext
import io.micronaut.context.annotation.Context
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.context.annotation.Value
import mu.KotlinLogging
import java.time.Duration
import javax.sql.DataSource

/** Adds retry to the original [DatasourceFactory] */
@Factory
@Requires(classes = [DatasourceFactory::class])
@Requires(property = "feature-toggles.persistence.improved-probes", value = "true", defaultValue = "true")
@Replaces(factory = DatasourceFactory::class)
class PlatformDatasourceFactory(
    applicationContext: ApplicationContext,
    @Value("\${datasource.init-connection-retry-delay:5s}") private val retryDelay: Duration,
    private val retryRegistry: RetryRegistry,
) : DatasourceFactory(applicationContext) {
    private val logger = KotlinLogging.logger { }

    private val retryConfig =
        RetryConfig
            .custom<DataSource>()
            .maxAttempts(Int.MAX_VALUE)
            .retryOnException { true }
            .waitDuration(retryDelay)
            .build()

    @Context
    @EachBean(DatasourceConfiguration::class)
    override fun dataSource(datasourceConfiguration: DatasourceConfiguration): DataSource =
        retryRegistry
            .retry("${this::class.qualifiedName}:${datasourceConfiguration.name}", retryConfig)
            .executeCallable {
                try {
                    super.dataSource(datasourceConfiguration)
                } catch (e: Exception) {
                    logger.error(e) {
                        "Failed to connect to postgres, will retry in [${retryDelay.toSeconds()}]s"
                    }

                    throw e
                }
            }
}
