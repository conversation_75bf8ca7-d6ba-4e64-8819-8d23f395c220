# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build System & Commands

**Build & Test Commands:**
```bash
# Build entire project
./gradlew build

# Run tests (use for verification after changes)
./gradlew test

# Code style check and format
./gradlew ktlintCheck
./gradlew ktlintFormat

# Publish locally for testing
./gradlew publishToMavenLocal

# Clean build
./gradlew clean
```

**Module-specific testing:**
```bash
# Test specific module
./gradlew :api:test
./gradlew :core:test
./gradlew :rabbitmq:test
```

## Project Architecture

This is a **shared library platform** for Kotlin microservices built on Micronaut framework. The architecture is modular with each module providing specific functionality:

**Core Modules:**
- `api/` - REST API layer with OpenAPI, client/server response mapping, exception handling
- `core/` - Essential functionality (health checks, time zones, readiness management)
- `security/` - Keycloak integration, authentication/authorization
- `rabbitmq/` - Messaging with retry mechanisms, dead letter queues, health indicators
- `redis-lettuce/` - Redis caching integration with Micronaut Cache
- `persistence/` - Database utilities and entity base classes

**Infrastructure Modules:**
- `jobs/` - Lightweight job execution framework (not Quartz-based)
- `quartz/` - Quartz-based job scheduling
- `hazelcast/` - Distributed cache/data grid
- `minio/` - Object storage integration
- `logging/` - Structured logging with Logback
- `kv-storage/` - Key-value storage abstraction

**Build & Testing:**
- `gradle-plugin/` - Custom Gradle plugins for build standardization
- `test-utils/` - Testing utilities and TestContainers helpers

## Key Design Patterns

**Error Handling:** All modules follow a consistent error handling pattern with client/server exception mapping. Check `api/src/main/kotlin/cz/pbktechnology/platform/common/client/mapper/` for the pattern.

**Health Management:** Each infrastructure module provides health indicators. Look at existing health indicator implementations when adding new ones.

**Context Propagation:** User context and correlation IDs are propagated across services. Use `ContextProvider` and `OperationContext` from the `api` module.

**Distributed Tracing:** The platform provides comprehensive OpenTelemetry-based distributed tracing:
- **W3C Trace Context propagation** for cross-service tracing (`OpenTelemetryUtils`)
- **Three instrumentation contexts**: Server (REST), Consumer (messaging), and Job contexts
- **Automatic span creation** with proper parent-child relationships
- **Correlation ID tracking** - Every operation has a UUID correlation ID
- **Context management** via Kotlin coroutines with thread-safe propagation
- **HTTP client instrumentation** (`PbkHttpClientFilter`) for automatic header propagation
- **Custom instrumenters** for messaging consumers and jobs with attribute extraction
- **MDC integration** for correlating logs with traces
- Location: `api/src/main/kotlin/cz/pbktechnology/platform/common/context/`

**Configuration:** All modules use Micronaut's configuration system with validation. The gradle-plugin includes configuration validation rules.

## Development Notes

**Kotlin Version:** 1.9.25 (check build.gradle.kts for current version)

**Testing:** Use TestContainers for integration tests. See existing test patterns in modules like `rabbitmq/`, `redis-lettuce/`, and `testservice/`.

**Code Style:** KtLint is enforced. Always run `./gradlew ktlintFormat` before committing.

**Module Creation:** Follow the existing module structure pattern - each module has its own README.md and follows the standard Gradle module layout.