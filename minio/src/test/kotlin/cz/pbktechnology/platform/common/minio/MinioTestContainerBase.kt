package cz.pbktechnology.platform.common.minio

import io.micronaut.test.support.TestPropertyProvider
import io.minio.BucketExistsArgs
import io.minio.MakeBucketArgs
import io.minio.MinioClient
import io.minio.SetBucketVersioningArgs
import io.minio.messages.VersioningConfiguration
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.TestInstance
import org.testcontainers.containers.MinIOContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
abstract class MinioTestContainerBase : TestPropertyProvider {
    companion object {
        const val TEST_BUCKET = "test-bucket"
        const val MINIO_IMAGE = "minio/minio:RELEASE.2025-02-28T09-55-16Z"

        @Container
        @JvmStatic
        val minioContainer = MinIOContainer(MINIO_IMAGE)
    }

    override fun getProperties() =
        mapOf(
            "minio.url" to minioContainer.s3URL,
            "minio.accessKey" to minioContainer.userName,
            "minio.secretKey" to minioContainer.password,
        )

    @BeforeAll
    fun setupBucket() {
        val minioClient =
            MinioClient
                .builder()
                .endpoint(minioContainer.s3URL)
                .credentials(minioContainer.userName, minioContainer.password)
                .build()

        if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(TEST_BUCKET).build())) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(TEST_BUCKET).build())
            minioClient.setBucketVersioning(
                SetBucketVersioningArgs
                    .builder()
                    .bucket(TEST_BUCKET)
                    .config(VersioningConfiguration(VersioningConfiguration.Status.ENABLED, null))
                    .build(),
            )
        }
    }
}
