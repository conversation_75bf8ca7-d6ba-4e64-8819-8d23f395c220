package cz.pbktechnology.platform.common.minio

import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.minio.MinioClient
import io.opentelemetry.api.OpenTelemetry
import jakarta.inject.<PERSON><PERSON>

@Singleton
@Replaces(MinioClient::class)
@Requires(missingBeans = [OpenTelemetry::class])
class StandardMinioClient(
    minioConfiguration: MinioConfiguration,
    properties: MinioConfigurationProperties,
) : MinioClient(
        builder()
            .endpoint(properties.url)
            .httpClient(
                minioConfiguration.newHttpClient(
                    connectTimeout = properties.connectionTimeout,
                    readTimeout = properties.readTimeout,
                    writeTimeout = properties.writeTimeout,
                ),
            ).also { builder ->
                if (properties.region.isNotEmpty()) {
                    builder.region(properties.region)
                }
            }.credentials(properties.accessKey, properties.secretKey)
            .build(),
    )
