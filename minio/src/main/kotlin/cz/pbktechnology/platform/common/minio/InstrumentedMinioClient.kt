package cz.pbktechnology.platform.common.minio

import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.instrument.InstrumenterFactory.Companion.MINIO_INSTRUMENTER
import cz.pbktechnology.platform.common.context.instrument.MinioRequest
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.core.annotation.Nullable
import io.minio.BucketExistsArgs
import io.minio.CopyObjectArgs
import io.minio.GetObjectArgs
import io.minio.GetObjectResponse
import io.minio.MakeBucketArgs
import io.minio.MinioClient
import io.minio.ObjectWriteResponse
import io.minio.PutObjectArgs
import io.minio.RemoveObjectArgs
import io.minio.StatObjectArgs
import io.minio.StatObjectResponse
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.context.Context
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter
import jakarta.inject.Named
import jakarta.inject.Singleton

@Singleton
@Replaces(MinioClient::class)
@Requires(beans = [OpenTelemetry::class])
class InstrumentedMinioClient(
    minioConfiguration: MinioConfiguration,
    properties: MinioConfigurationProperties,
    @Nullable
    @Named(MINIO_INSTRUMENTER)
    private val minioInstrumenter: Instrumenter<MinioRequest, Any>?,
) : MinioClient(
        builder()
            .endpoint(properties.url)
            .httpClient(
                minioConfiguration.newHttpClient(
                    connectTimeout = properties.connectionTimeout,
                    readTimeout = properties.readTimeout,
                    writeTimeout = properties.writeTimeout,
                ),
            ).also { builder ->
                if (properties.region.isNotEmpty()) {
                    builder.region(properties.region)
                }
            }.credentials(properties.accessKey, properties.secretKey)
            .build(),
    ) {
    init {
        println("InstrumentedMinioClient created with instrumenter: ${minioInstrumenter != null}")
    }

    override fun getObject(args: GetObjectArgs): GetObjectResponse =
        withSpan(args.bucket(), args.`object`(), "getObject") {
            super.getObject(args)
        }

    override fun putObject(args: PutObjectArgs): ObjectWriteResponse =
        withSpan(args.bucket(), args.`object`(), "putObject") {
            super.putObject(args)
        }

    override fun copyObject(args: CopyObjectArgs): ObjectWriteResponse =
        withSpan(args.bucket(), args.`object`(), "copyObject") {
            super.copyObject(args)
        }

    override fun removeObject(args: RemoveObjectArgs) {
        withSpan(args.bucket(), args.`object`(), "removeObject") {
            super.removeObject(args)
        }
    }

    override fun statObject(args: StatObjectArgs): StatObjectResponse =
        withSpan(args.bucket(), args.`object`(), "statObject") {
            super.statObject(args)
        }

    override fun bucketExists(args: BucketExistsArgs): Boolean =
        withSpan(args.bucket(), "", "bucketExists") {
            super.bucketExists(args)
        }

    override fun makeBucket(args: MakeBucketArgs) {
        withSpan(args.bucket(), "", "makeBucket") {
            super.makeBucket(args)
        }
    }

    private fun <T> withSpan(
        bucketName: String,
        objectName: String,
        operationName: String,
        action: () -> T,
    ): T {
        if (minioInstrumenter == null) {
            return action()
        }

        val operationContext = ContextConfiguration.operationContext.get()
        val correlationId = operationContext?.xCorrelationId?.toString() ?: "unknown"

        val request =
            MinioRequest(
                correlationId = correlationId,
                bucketName = bucketName,
                objectName = objectName,
                operationName = operationName,
            )

        return minioInstrumenter.executeWithSpan(request, action)
    }
}

private fun <REQUEST : Any, RESPONSE> Instrumenter<REQUEST, Any>.executeWithSpan(
    request: REQUEST,
    action: () -> RESPONSE,
): RESPONSE {
    val otelContext = start(Context.current(), request)
    val scope = otelContext.makeCurrent()
    return try {
        val result = action()
        end(otelContext, request, result, null)
        result
    } catch (e: Exception) {
        end(otelContext, request, null, e)
        throw e
    } finally {
        scope.close()
    }
}
