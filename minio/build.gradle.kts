plugins { id("cz.pbktechnology.platform.common.gradle-plugin.module") }

platform { publishing { artifactId = "minio" } }

val kotlinVersion: String by project
val micronautVersion: String by project
val minioVersion: String by project
val ioArrowVersion: String by project
val coroutinesVersion: String by project
val jacksonVersion: String by project

dependencies {
    implementation(platform(project(":bom")))
    kapt(platform(project(":bom")))

    implementation(project(":api"))

    // to be injectable in another projects
    kapt("io.micronaut:micronaut-inject-java")
    // to compile annotations
    implementation("io.micronaut:micronaut-inject")

    api("io.minio:minio")

    implementation("io.arrow-kt:arrow-core")
    implementation("io.micronaut:micronaut-http")
    implementation("io.micronaut:micronaut-management")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("io.micronaut.tracing:micronaut-tracing-opentelemetry-http")

    testImplementation("io.strikt:strikt-arrow")
    testImplementation("io.mockk:mockk")
    testImplementation(kotlin("test-junit5"))

    implementation("io.github.microutils:kotlin-logging-jvm")
    testImplementation("io.micronaut.test:micronaut-test-junit5")
    testImplementation(project(":logging-logback"))
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:minio:1.20.4")
    testImplementation("io.opentelemetry:opentelemetry-sdk-testing")
    testImplementation("org.assertj:assertj-core")
    kaptTest("io.micronaut:micronaut-inject-java")
}

tasks.test { useJUnitPlatform() }
