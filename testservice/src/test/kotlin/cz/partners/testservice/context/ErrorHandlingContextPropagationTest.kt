package cz.partners.testservice.context

import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.databind.exc.InvalidFormatException
import cz.partners.testservice.generated.v1.client.TestApiClient
import cz.partners.testservice.generated.v1.server.TestApiAdapter
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.ContextProvider
import cz.pbktechnology.platform.common.context.Header
import cz.pbktechnology.platform.common.context.instrument.server.CorrelationIdServerExtractor
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.exception.ValidationException
import cz.pbktechnology.platform.common.exception.handler.ConstraintViolationExceptionHandler
import cz.pbktechnology.platform.common.exception.handler.PbkContextAwareHandler
import cz.pbktechnology.platform.common.helper.responsemapper.ServerResponseMapperHolder
import cz.pbktechnology.platform.common.test.util.EitherTestExtension.assertIsLeft
import cz.pbktechnology.platform.testservice.probe.Capture
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.core.convert.exceptions.ConversionErrorException
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import jakarta.inject.Inject
import jakarta.inject.Singleton
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.slf4j.MDC
import javax.validation.ConstraintViolation
import javax.validation.ConstraintViolationException

@MicronautTest(environments = ["error"])
class ErrorHandlingContextPropagationTest(
    private val contextProvider: ContextProvider,
    private val testApiClient: TestApiClient,
) {
    // leftover from other test
    @MockBean(Capture::class)
    fun mockCapture() = mockk<Capture>()

    @MockBean(TestApiAdapter::class)
    fun mock() =
        object : TestApiAdapter {
            override suspend fun designfirstlegacyendpoint(propagateUserContext: Boolean): Either<PbkException, Unit> = Unit.right()

            override suspend fun error(argument: String): Either<PbkException, Unit> =
                when (argument) {
                    "runtime" -> throw RuntimeException("runtime exception")
                    "constraint" -> throw ConstraintViolationException(HashSet<ConstraintViolation<*>>(1))
                    "conversion" ->
                        throw ConversionErrorException(Argument.INT, ArithmeticException("Divide by zero"))

                    "json" ->
                        throw InvalidFormatException(
                            "You didn't spell it [že'són] (French accent intensifies)",
                            "",
                            String::class.java,
                        )

                    "validation" -> throw ValidationException("I am a motorcycle, your data is invalid")
                    else -> TODO("Intentional Not yet implemented")
                }

            override suspend fun dummy(argument: String): Either<PbkException, String> = "".right()
        }

    private val logger = KotlinLogging.logger {}

    @Inject
    lateinit var testConstraintViolationExceptionHandler: TestConstraintViolationExceptionHandler

    @Test
    fun `correlation Id is propagated from coroutine context to MDC in ExceptionHandler`() {
        var correlationId: String? = "NOPE"
        runBlocking {
            contextProvider.withJobContext("Error Handling test") {
                correlationId =
                    ContextConfiguration.operationContext
                        .get()
                        .xCorrelationId
                        .toString()
                testApiClient.error(argument = "constraint").assertIsLeft()
            }

            assertThat(testConstraintViolationExceptionHandler.lastCalledCorrelationId).isEqualTo(correlationId)
        }
    }

    @Test
    fun `all cases of exception handler logs`() {
        runBlocking {
            contextProvider.withJobContext("Error Handling test") {
                testApiClient.error(argument = "").assertIsLeft()
                logger.info { "-----------------------------" }
            }

            contextProvider.withJobContext("Error Handling test") {
                testApiClient.error(argument = "runtime").assertIsLeft()
                logger.info { "-----------------------------" }
            }

            contextProvider.withJobContext("Error Handling test") {
                testApiClient.error(argument = "constraint").assertIsLeft()
                logger.info { "-----------------------------" }
            }

            contextProvider.withJobContext("Error Handling test") {
                testApiClient.error(argument = "conversion").assertIsLeft()
                logger.info { "-----------------------------" }
            }

            contextProvider.withJobContext("Error Handling test") {
                testApiClient.error(argument = "json").assertIsLeft()
                logger.info { "-----------------------------" }
            }

            contextProvider.withJobContext("Error Handling test") {
                testApiClient.error(argument = "validation").assertIsLeft()
                logger.info { "-----------------------------" }
            }
        }
    }

    @Singleton
    @Requires(env = ["error"])
    @Replaces(bean = ConstraintViolationExceptionHandler::class)
    class TestConstraintViolationExceptionHandler(
        serverResponseMapperHolder: ServerResponseMapperHolder,
        correlationIdServerExtractor: CorrelationIdServerExtractor,
    ) : PbkContextAwareHandler<ConstraintViolationException>(
            serverResponseMapperHolder = serverResponseMapperHolder,
            correlationIdServerExtractor = correlationIdServerExtractor,
        ) {
        var lastCalledCorrelationId: String? = null

        override fun handleInContext(
            request: HttpRequest<*>,
            operation: String,
            exception: ConstraintViolationException,
        ): PbkException {
            lastCalledCorrelationId = MDC.get(Header.X_CORRELATION_ID)
            return cz.pbktechnology.platform.common.exception.ConstraintViolationException(
                emptyList(),
                "from test mapper, mock class",
            )
        }
    }
}
