package cz.pbktechnology.platform.common.rabbitmq.improvedprobes

import com.fasterxml.jackson.databind.ObjectMapper
import com.rabbitmq.client.AMQP
import com.rabbitmq.client.BuiltinExchangeType
import com.rabbitmq.client.Channel
import cz.pbktechnology.platform.common.context.ContextProvider
import cz.pbktechnology.platform.common.core.health.ApplicationHealth
import cz.pbktechnology.platform.common.rabbitmq.Consumer
import cz.pbktechnology.platform.common.rabbitmq.ConsumerRegistration
import cz.pbktechnology.platform.common.rabbitmq.ConsumerRegistrations
import cz.pbktechnology.platform.common.rabbitmq.IgnoreQueueCheck
import cz.pbktechnology.platform.common.rabbitmq.MessagingExceptionLoggingHandler
import cz.pbktechnology.platform.common.rabbitmq.config.ConsumerConfig
import cz.pbktechnology.platform.common.rabbitmq.config.DeclareQueuesConfig
import cz.pbktechnology.platform.common.rabbitmq.config.ProducerConfig
import cz.pbktechnology.platform.common.rabbitmq.config.RabbitMqConfig
import cz.pbktechnology.platform.common.util.ExceptionUtil.localizedMessageWithFallback
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.micronaut.context.annotation.Requires
import io.opentelemetry.context.Context
import jakarta.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.reactor.asFlux
import mu.KotlinLogging
import reactor.rabbitmq.AcknowledgableDelivery
import reactor.rabbitmq.ConsumeOptions
import reactor.rabbitmq.RabbitFlux
import reactor.rabbitmq.ReceiverOptions
import java.nio.charset.Charset
import java.time.Duration
import java.util.UUID
import javax.annotation.PostConstruct
import javax.sql.DataSource

@OptIn(ExperimentalCoroutinesApi::class)
@Suppress("unused")
@Singleton
@Requires(property = "feature-toggles.rabbitmq.improved-probes", value = "true", defaultValue = "true")
open class RabbitConfigurationImprovedProbes(
    consumerConfigList: List<ConsumerConfig>,
    producerConfigList: List<ProducerConfig>,
    declareQueuesConfig: DeclareQueuesConfig,
    val rabbitConnection: RabbitConnection,
    val config: RabbitMqConfig,
    val objectMapper: ObjectMapper,
    val contextProvider: ContextProvider,
    val messagingExceptionLoggingHandler: MessagingExceptionLoggingHandler,
    val readinessListenerRegistry: ReadinessListenerRegistry,
    val applicationHealth: ApplicationHealth,
    val ignoreQueuesCheck: List<IgnoreQueueCheck>,
    val retryRegistry: RetryRegistry,
) {
    private val retryConfig =
        RetryConfig
            .custom<DataSource>()
            .maxAttempts(Int.MAX_VALUE)
            .retryOnException { true }
            .waitDuration(config.recoveryRetryDelay)
            .build()

    val consumerConfig = consumerConfigList.associateBy { it.name }
    private val producerConfig = producerConfigList.associateBy { it.name }

    val declareQueues = declareQueuesConfig.getIsEnabled()

    /**
     * Used for setting up queues and exchanges
     */
    private lateinit var channel: Channel

    /** List of channels that should be open */
    val openedChannels = mutableListOf<Channel>()

    @PostConstruct
    fun init() {
        rabbitConnection.registerDisconnectListener {
            // When connection is lost all channels are closed
            openedChannels.clear()
        }

        // No need to add it to `openedChannels` it's needed only in start of the application
        channel = rabbitConnection.connection.createChannel()

        logger.info { "RabbitMQ Connection created" }

        if (declareQueues) {
            consumerConfig.forEach { declareConsumer(it.value) }
            producerConfig.forEach { declareProducer(it.value) }
        }

        if (config.checkQueues) {
            consumerConfig
                .filter { (queueName, _) -> ignoreQueuesCheck.none { it.getQueueName() == queueName } }
                .forEach { (_, config) ->
                    checkConsumerQueues(channel, config)
                }
            producerConfig
                .filter { (queueName, _) -> ignoreQueuesCheck.none { it.getQueueName() == queueName } }
                .forEach { (_, config) ->
                    checkProducerQueues(channel, config)
                }
        }
    }

    fun <T> initProducer(producerName: String): ProducerImprovedProbesImpl<T> =
        producerConfig[producerName]?.let { createProducer(it) }
            ?: error("Undefined producer requested: [$producerName]")

    fun <T> createProducer(producerConfig: ProducerConfig): ProducerImprovedProbesImpl<T> {
        logger.debug("Creating producer: [${producerConfig.name}] with config: [$producerConfig]")
        return ProducerImprovedProbesImpl(
            objectMapper = objectMapper,
            exchangeName = producerConfig.exchange,
            connection = rabbitConnection,
            onNewChannel = {
                openedChannels.add(it)
            },
            routingKey = producerConfig.routingKey,
            config = config,
            retryRegistry = retryRegistry,
        )
    }

    inline fun <reified T> initConsumer(
        consumerName: String,
        process: Consumer<T>,
    ): ConsumerRegistrations {
        val consumerConfig =
            consumerConfig[consumerName] ?: error("Undefined consumer requested: [$consumerName]")
        val consumerRegistrations =
            if (applicationHealth.isReady()) {
                ConsumerRegistrations(createAsyncConsumer(consumerConfig, consumerName, process))
            } else {
                logger.info("Not ready, postponing consumer creation [{}]", consumerName)
                ConsumerRegistrations(emptyMap())
            }

        val registerFn = {
            retryRegistry.retry("${this::class.qualifiedName}:$consumerName").executeCallable {
                try {
                    createAsyncConsumer<T>(consumerConfig, consumerName, process).also {
                        consumerRegistrations.consumerMap = it
                    }
                } catch (e: Exception) {
                    logger.error(e) { "Failed to crate consumer, will retry in [${config.recoveryRetryDelay}]s" }
                }
            }
        }

        val readinessListener: ReadinessListener = { ready ->
            if (ready) {
                logger.info("Application ready, registering consumer [$consumerName]")
                registerFn()
            } else {
                logger.info("Application not ready, unregistering consumer [$consumerName]")
                terminateConsumerInternal(consumerRegistrations)
            }
        }

        readinessListenerRegistry.registerListener(readinessListener)
        consumerRegistrations.readinessListener = readinessListener

        rabbitConnection.registerReconnectListener {
            if (applicationHealth.isReady()) {
                logger.info("RabbitMQ reconnected, registering consumer $consumerName")
                registerFn()
            }
        }

        return consumerRegistrations
    }

    fun terminateConsumer(consumerRegistrations: ConsumerRegistrations) {
        readinessListenerRegistry.unregisterListener(consumerRegistrations.readinessListener!!)

        terminateConsumerInternal(consumerRegistrations)
    }

    /**
     * Do not call directly, use [terminateConsumer] instead. Public because of inlined
     * [initConsumer]
     */
    fun terminateConsumerInternal(consumerRegistrations: ConsumerRegistrations) {
        consumerRegistrations.consumerMap.forEach { (_, consumerRegistration) ->
            openedChannels.removeIf {
                listOf(
                    consumerRegistration.retryPublishChannel?.channelNumber,
                    consumerRegistration.consumerChannel?.channelNumber,
                ).contains(it.channelNumber)
            }

            // It may already be closed due to connection error
            runCatching { consumerRegistration.retryPublishChannel?.close() }
                .exceptionOrNull()
                ?.also { logger.warn(it) { "Failed to close channel" } }

            // It may already be closed due to connection error
            runCatching { consumerRegistration.consumer.dispose() }
                .exceptionOrNull()
                ?.also { logger.warn(it) { "Failed to close channel" } }
        }

        consumerRegistrations.consumerMap = emptyMap()
    }

    /** Do not use directly, use [initConsumer] instead. Public because of inlined [initConsumer] */
    inline fun <reified T> createAsyncConsumer(
        consumerConfig: ConsumerConfig,
        consumerName: String,
        process: Consumer<T>,
    ): Map<String, ConsumerRegistration> {
        logger.debug("Creating consumer [$consumerName] with config: [$consumerConfig]")

        // create channel for each consumer
        val consumerRegistrationMap =
            (1..consumerConfig.consumerChannelCount).associate {
                val receiver =
                    RabbitFlux.createReceiver(ReceiverOptions().connectionSupplier { rabbitConnection.connection })

                val retryPublishChannel = rabbitConnection.connection.createChannel()
                openedChannels.add(retryPublishChannel)

                val consumerTag = "${config.consumerTagPrefix}:$consumerName:${UUID.randomUUID()}"
                val consumerOptions = getConsumeOptions(consumerTag, consumerConfig)
                val consumer =
                    receiver
                        .consumeManualAck(consumerConfig.queue, consumerOptions)
                        .map {
                            it to Context.current() // This context is instrumented by OneAgent and
                            // is important in connecting the tracing
                        }.asFlow()
                        .onEach { (acknowledgableDelivery, context) ->
                            context.makeCurrent() // Restore instrumented context on different
                            // thread
                            delay(consumerConfig.throttleDelayMs)
                            handleDelivery(acknowledgableDelivery, process, retryPublishChannel, consumerConfig)
                        }.flowOn(Dispatchers.IO.limitedParallelism(prefetchCount(consumerConfig)))
                        .asFlux()
                        .subscribe()

                val consumerRegistration = ConsumerRegistration(consumer, null, retryPublishChannel)
                consumerOptions.channelCallback {
                    consumerRegistration.consumerChannel = it
                    openedChannels.add(it)
                }

                consumerTag to consumerRegistration
            }

        return consumerRegistrationMap
    }

    suspend inline fun <reified T> handleDelivery(
        delivery: AcknowledgableDelivery?,
        process: Consumer<T>,
        retryPublishChannel: Channel,
        consumerConfig: ConsumerConfig,
    ) {
        delivery?.let {
            try {
                val headers =
                    it.properties.headers.mapValues { it.value?.toString() ?: "undefined" }
                contextProvider.withConsumerContext(consumerConfig.name, headers) {
                    handleMessage(
                        it,
                        process,
                        retryPublishChannel,
                        consumerConfig,
                        objectMapper,
                        messagingExceptionLoggingHandler,
                    )
                }
            } catch (t: Throwable) {
                moveToDeadQueue(
                    it,
                    t,
                    retryPublishChannel,
                    consumerConfig.queue,
                    consumerConfig.exchange,
                )
            }
        } ?: logger.error { "Null delivery received on [$consumerConfig]" }
    }

    private fun declareConsumer(config: ConsumerConfig) {
        logger.info { "Declaring queue and exchange for consumer: [${config.name}]" }

        with(config) {
            declareQueue(
                exchange = exchange,
                queue = queue,
                routingKey = routingKey,
                durable = durable,
                exclusive = exclusive,
                autodelete = autodelete,
            )
            declareQueue(
                exchange = exchange + ".$DEAD",
                queue = queue + ".$DEAD",
                routingKey = "",
                durable = durable,
                exclusive = exclusive,
                autodelete = autodelete,
            )
            retryDelay?.let {
                logger.info { "Declaring delay queue and exchange for consumer: [${config.name}]" }
                declareDelayQueue(
                    exchange = exchange,
                    queue = queue,
                    routingKey = "",
                    durable = durable,
                    exclusive = exclusive,
                    autodelete = autodelete,
                    delay = it,
                )
            }
        }
    }

    private fun declareProducer(config: ProducerConfig) {
        logger.info { "Declaring queue and exchange for producer: [${config.name}]" }

        with(config) {
            declareQueue(
                exchange = exchange,
                queue = queue,
                routingKey = routingKey,
                durable = durable,
                exclusive = exclusive,
                autodelete = autodelete,
            )
            declareQueue(
                exchange = exchange + ".$DEAD",
                queue = queue + ".$DEAD",
                routingKey = "",
                durable = durable,
                exclusive = exclusive,
                autodelete = autodelete,
            )
        }
    }

    private fun declareQueue(
        exchange: String,
        queue: String,
        routingKey: String,
        durable: Boolean,
        exclusive: Boolean,
        autodelete: Boolean,
    ) {
        channel.exchangeDeclare(exchange, BuiltinExchangeType.DIRECT)
        channel.queueDeclare(queue, durable, exclusive, autodelete, null)
        channel.queueBind(queue, exchange, routingKey)
    }

    private fun declareDelayQueue(
        exchange: String,
        queue: String,
        routingKey: String,
        durable: Boolean,
        exclusive: Boolean,
        autodelete: Boolean,
        delay: Duration,
    ) {
        val arguments =
            mapOf(
                QUEUE_ARGUMENT_X_MESSAGE_TTL to delay.toMillis(),
                QUEUE_ARGUMENT_X_DEAD_LETTER_EXCHANGE to exchange,
            )

        val delayQueue = delayedQueue(queue, delay)
        val delayExchange = delayedExchange(exchange, delay)

        channel.exchangeDeclare(delayExchange, BuiltinExchangeType.DIRECT)
        channel.queueDeclare(delayQueue, durable, exclusive, autodelete, arguments)
        channel.queueBind(delayQueue, delayExchange, routingKey)
    }

    private fun checkConsumerQueues(
        channel: Channel,
        config: ConsumerConfig,
    ) {
        channel.queueDeclarePassive(config.queue)
        channel.exchangeDeclarePassive(config.exchange)
        channel.queueDeclarePassive("${config.queue}.$DEAD")
        channel.exchangeDeclarePassive(deadExchange(config.exchange))
        config.retryDelay?.let { delay ->
            channel.queueDeclarePassive(delayedQueue(config.queue, delay))
            channel.exchangeDeclarePassive(delayedExchange(config.exchange, delay))
        }
    }

    private fun checkProducerQueues(
        channel: Channel,
        config: ProducerConfig,
    ) {
        channel.queueDeclarePassive(config.queue)
        channel.exchangeDeclarePassive(config.exchange)
        channel.queueDeclarePassive("${config.queue}.$DEAD")
        channel.exchangeDeclarePassive(deadExchange(config.exchange))
    }

    companion object {
        const val DEAD = "dead"
        const val DELAY = "delay"
        private const val HEADER_X_RETRY = "x-retry"
        private const val QUEUE_ARGUMENT_X_MESSAGE_TTL = "x-message-ttl"
        private const val QUEUE_ARGUMENT_X_DEAD_LETTER_EXCHANGE = "x-dead-letter-exchange"
        private const val QUEUE_ARGUMENT_X_EXPIRES = "x-expires"
        private const val HEADER_X_ERROR_MESSAGE = "x-error-message"
        const val DEFAULT_PREFETCH_COUNT = 2

        val logger = KotlinLogging.logger {}

        inline fun <reified T> mapDelivery(
            bytes: ByteArray,
            objectMapper: ObjectMapper,
        ): T = objectMapper.readValue(String(bytes, Charset.defaultCharset()), T::class.java)

        suspend inline fun <reified T> handleMessage(
            delivery: AcknowledgableDelivery,
            process: Consumer<T>,
            retryPublishChannel: Channel,
            consumerConfig: ConsumerConfig,
            objectMapper: ObjectMapper,
            messagingExceptionLoggingHandler: MessagingExceptionLoggingHandler,
        ) {
            val maxRetry = consumerConfig.maxRetry
            val retryDelay = consumerConfig.retryDelay
            val originalQueue = consumerConfig.queue
            val originalExchange = consumerConfig.exchange

            try {
                process
                    .accept(
                        mapDelivery(delivery.body, objectMapper),
                        delivery.body,
                        delivery.properties.headers,
                    )?.mapLeft {
                        messagingExceptionLoggingHandler.handleEither(it, delivery.properties, maxRetry, originalQueue)
                        retryOrDead(
                            originalQueue,
                            originalExchange,
                            delivery.properties,
                            delivery.body,
                            it.localizedMessageWithFallback(),
                            maxRetry,
                            retryDelay,
                            retryPublishChannel,
                        )
                    }
                delivery.ack(false)
            } catch (e: Exception) {
                messagingExceptionLoggingHandler.handle(e, delivery.properties, maxRetry, originalQueue)
                retryOrDead(
                    originalQueue,
                    originalExchange,
                    delivery.properties,
                    delivery.body,
                    e.localizedMessageWithFallback(),
                    maxRetry,
                    retryDelay,
                    retryPublishChannel,
                )
                delivery.ack(false)
            }
        }

        fun retryOrDead(
            queue: String,
            originalExchange: String,
            properties: AMQP.BasicProperties,
            body: ByteArray,
            localizedMessage: String,
            maxRetry: Int,
            retryDelay: Duration?,
            retryPublishChannel: Channel,
        ) {
            val retries = (properties.headers[HEADER_X_RETRY] ?: 0).toString().toInt() + 1

            if (retries <= maxRetry || maxRetry == -1) {
                properties.headers[HEADER_X_RETRY] = retries

                retryDelay?.let {
                    val delayExchange = delayedExchange(originalExchange, retryDelay)
                    logger.info(
                        "Publishing message from [$queue] to [$delayExchange] with [$retries] attempt.",
                    )
                    retryPublishChannel.basicPublish(delayExchange, "", properties, body)
                }
                    ?: run {
                        logger.info(
                            "Publishing message from [$queue] to [$originalExchange] with [$retries] attempt.",
                        )
                        retryPublishChannel.basicPublish(originalExchange, "", properties, body)
                    }
            } else {
                val deadExchange = deadExchange(originalExchange)
                logger.warn { "Publishing message from [$queue] to [$deadExchange]" }
                properties.headers[HEADER_X_ERROR_MESSAGE] = localizedMessage
                retryPublishChannel.basicPublish(deadExchange, "", properties, body)
            }
        }

        fun moveToDeadQueue(
            delivery: AcknowledgableDelivery,
            t: Throwable,
            retryPublishChannel: Channel,
            queue: String,
            exchange: String,
        ) {
            logger.error(t) { "Exception occured when handling message" }
            delivery.properties.headers[HEADER_X_ERROR_MESSAGE] = t.localizedMessageWithFallback()
            val deadExchange = deadExchange(exchange)
            logger.info { "Publishing message from [$queue] to [$deadExchange]" }

            retryPublishChannel.basicPublish(deadExchange, "", delivery.properties, delivery.body)
            delivery.ack(false)
        }

        private fun deadExchange(originalExchange: String) = originalExchange + "." + DEAD

        private fun delayedExchange(
            originalExchange: String,
            delay: Duration,
        ) = originalExchange + "." + DELAY + "-" + delay.toMillis()

        private fun delayedQueue(
            originalQueue: String,
            delay: Duration,
        ) = originalQueue + "." + DELAY + "-" + delay.toMillis()

        fun getConsumeOptions(
            consumerTag: String,
            consumerConfig: ConsumerConfig,
        ) = ConsumeOptions().consumerTag(consumerTag).qos(prefetchCount(consumerConfig))

        fun prefetchCount(consumerConfig: ConsumerConfig) = consumerConfig.prefetchCount ?: DEFAULT_PREFETCH_COUNT
    }
}
