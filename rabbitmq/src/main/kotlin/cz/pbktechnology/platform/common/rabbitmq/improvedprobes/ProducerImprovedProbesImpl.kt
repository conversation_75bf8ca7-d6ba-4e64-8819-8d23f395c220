package cz.pbktechnology.platform.common.rabbitmq.improvedprobes

import com.fasterxml.jackson.databind.ObjectMapper
import com.rabbitmq.client.AMQP
import com.rabbitmq.client.Channel
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.Header
import cz.pbktechnology.platform.common.context.OpenTelemetryUtils
import cz.pbktechnology.platform.common.logging.SensitivityLevel
import cz.pbktechnology.platform.common.rabbitmq.Producer
import cz.pbktechnology.platform.common.rabbitmq.config.RabbitMqConfig
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import mu.KotlinLogging
import java.time.OffsetDateTime
import javax.sql.DataSource

/**
 * this does not switch threads on it's own, the publish is synchronous and in context of threads
 * unpredictable This is mainly because of blocking db In V4 it should be migrated to coroutine
 * //todo PTS-687
 */
class ProducerImprovedProbesImpl<T>(
    private val objectMapper: ObjectMapper,
    private val exchangeName: String,
    private val connection: RabbitConnection,
    private val routingKey: String,
    private val onNewChannel: (Channel) -> Unit,
    private val config: RabbitMqConfig,
    retryRegistry: RetryRegistry,
) : Producer<T> {
    private val logger = KotlinLogging.logger {}
    private val createRetry =
        retryRegistry.retry(
            "${this::class.qualifiedName}:create",
            RetryConfig
                .custom<DataSource>()
                .maxAttempts(Int.MAX_VALUE)
                .retryOnException { true }
                .waitDuration(config.recoveryRetryDelay)
                .build(),
        )
    private var channel: Channel =
        createChannelWithRetry()
            .also { onNewChannel(it) }

    init {
        connection.registerReconnectListener {
            logger.info { "RabbitMQ reconnected, creating new publish channel for $exchangeName" }
            channel = createChannelWithRetry()
            onNewChannel(channel)
        }
    }

    private fun createChannelWithRetry() =
        createRetry.executeCallable {
            try {
                connection.connection
                    .createChannel()
            } catch (e: Exception) {
                logger.error(e) { "Failed to create RabbitMQ channel, will retry in [${config.recoveryRetryDelay}]s" }
                throw e
            }
        }

    override fun publish(data: T) {
        val bytes = objectMapper.writeValueAsBytes(data)

        val xCorrelationId = ContextConfiguration.operationContext.get().xCorrelationId

        val headers =
            mutableMapOf<String, String>().apply {
                put(Header.X_CORRELATION_ID, xCorrelationId.toString())
                put(Header.X_TIMESTAMP, OffsetDateTime.now().toString())
            }

        OpenTelemetryUtils.injectCurrentOtelContextInto(headers)

        ContextConfiguration.operationContext.get().xUserContext?.let {
            headers.put(Header.X_USER_CONTEXT, it)
        }

        val properties =
            AMQP.BasicProperties
                .Builder()
                .deliveryMode(DELIVERY_MODE_PERSISTENT)
                .headers(headers as Map<String, Any>)
                .build()

        logger.trace(SensitivityLevel.HIGH.marker) {
            "Sending to exchange: [$exchangeName] message with headers: [$headers], routing key: [$routingKey]"
        }
        channel.basicPublish(exchangeName, routingKey, properties, bytes)
    }

    companion object {
        const val DELIVERY_MODE_PERSISTENT = 2
    }
}
