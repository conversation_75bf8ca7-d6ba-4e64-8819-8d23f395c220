package cz.pbktechnology.platform.common.rabbitmq.improvedprobes

import com.rabbitmq.client.Connection
import com.rabbitmq.client.ConnectionFactory
import cz.pbktechnology.platform.common.rabbitmq.config.RabbitMqConfig
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import mu.KotlinLogging
import javax.sql.DataSource

@Singleton
@Requires(property = "feature-toggles.rabbitmq.improved-probes", value = "true", defaultValue = "true")
class RabbitConnection(
    private val config: RabbitMqConfig,
    private val connectionFactory: ConnectionFactory,
    private val rabbitMqConfig: RabbitMqConfig,
    retryRegistry: RetryRegistry,
) {
    private val logger = KotlinLogging.logger {}
    private val acquireConnectionRetry =
        retryRegistry.retry(
            "${this::class.qualifiedName}:acquireConnection",
            RetryConfig
                .custom<DataSource>()
                .maxAttempts(Int.MAX_VALUE)
                .retryOnException { true }
                .waitDuration(rabbitMqConfig.recoveryRetryDelay)
                .build(),
        )
    private var disconnectListeners = mutableListOf<() -> Unit>()
    private var reconnectListeners = mutableListOf<() -> Unit>()

    lateinit var connection: Connection
        private set

    init {
        setupConnectionFactory()
        acquireConnectionWithRetry()
    }

    fun registerDisconnectListener(action: () -> Unit) {
        disconnectListeners.add(action)
    }

    fun registerReconnectListener(action: () -> Unit) {
        reconnectListeners.add(action)
    }

    private fun setupConnectionFactory() =
        with(config) {
            connectionFactory.username = username
            connectionFactory.password = password
            connectionFactory.host = host
            connectionFactory.port = port
            connectionFactory.useNio()
            // We don't want to recover channels and consumers. To do that, we unfortunately need to turn everything off
            connectionFactory.isAutomaticRecoveryEnabled = false
        }

    private fun acquireConnectionWithRetry() {
        acquireConnectionRetry.executeCallable {
            try {
                acquireConnection()
            } catch (e: Exception) {
                logger.error(e) {
                    "Failed to connect to RabbitMQ, will retry in [${rabbitMqConfig.recoveryRetryDelay.toSeconds()}]s"
                }
                throw e
            }
        }
    }

    private fun acquireConnection() {
        this.connection =
            (connectionFactory.newConnection()).apply {
                addShutdownListener {
                    logger.warn { "RabbitMQ disconnected, initiating retry" }
                    disconnectListeners.forEach { it() }
                    acquireConnectionWithRetry()
                }
            }

        logger.info { "Connected to RabbitMQ" }
        reconnectListeners.forEach { it() }
    }
}
