plugins { id("cz.pbktechnology.platform.common.gradle-plugin.module") }

platform { publishing { artifactId = "rabbitmq" } }

dependencies {
    implementation(platform(project(":bom")))
    kapt(platform(project(":bom")))
    implementation(project(":api"))
    implementation(project(":core"))
    implementation(project(":logging"))

    // To be injectable in another projects
    kapt("io.micronaut:micronaut-inject-java")
    // To compile annotations
    implementation("io.micronaut:micronaut-inject")

    implementation("com.rabbitmq:amqp-client")
    implementation("io.projectreactor.rabbitmq:reactor-rabbitmq:1.5.4")

    implementation("com.fasterxml.jackson.core:jackson-databind")

    implementation("io.micronaut:micronaut-management")

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactive")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-jdk8")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j")

    implementation("io.arrow-kt:arrow-core")
    implementation("io.opentelemetry:opentelemetry-context")

    implementation("io.github.microutils:kotlin-logging-jvm")

    // Test
    testImplementation(kotlin("test-junit5"))
    kaptTest("io.micronaut:micronaut-inject-java")
    testImplementation("io.micronaut.test:micronaut-test-junit5")
    testImplementation("io.mockk:mockk")
    testImplementation(project(":logging-logback"))
    testImplementation(project(":core-test"))
    testImplementation("org.awaitility:awaitility-kotlin:4.2.0")
    testImplementation("org.assertj:assertj-core:3.24.2")
    testImplementation("io.micronaut:micronaut-http-server-netty")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testImplementation("org.testcontainers:rabbitmq")
    testImplementation("org.testcontainers:toxiproxy")
    testImplementation("org.junit.jupiter:junit-jupiter-params")

    /*
     * Fix issue with missing org.apache.commons.codec.Charsets class needed by
     * testcontainers
     */
    testRuntimeOnly("commons-codec:commons-codec:1.16.1")
}

tasks.test { useJUnitPlatform() }
